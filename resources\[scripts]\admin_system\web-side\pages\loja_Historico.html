
<div id="local">
   <div class="cards-2">
      <div class="card mb-0" v-for="(c, index) in resultado" :key="index">
         <div class="body text-center cursor-pointer" @click="configPurchase(c)">
            <h5 class="mb-2">{{ c.status }}</h5>
            <p class="text-muted m-b-0">
               <i class="far fa-calendar normal font-13 text-primary me-2"></i><small>Data:</small> 
               {{ new Date(c.dataCompra.year +'/'+ c.dataCompra.monthValue +'/'+ c.dataCompra.dayOfMonth).toLocaleDateString('pt-BR') }}
            </p>
            <p class="text-muted m-b-0">
               <i class="far fa-coins normal font-13 text-warning me-2"></i> {{ parseInt(c.valorTotal) }}
            </p>
         </div>
      </div>
   </div>
   <div v-if="resultado == null || resultado.length == 0">Nenhuma compra encontrada.</div>

   <!-- Modal compra -->
   <div class="modal fade" id="modalCompra" role="dialog">
      <div class="modal-dialog modal-lg" role="document">
         <div class="modal-content">
            <div class="modal-header">
               <h4 class="title" id="modalCompraLabel">Compra</h4>
            </div>
            <div class="modal-body py-3">
               <div v-if="compra == null || compra.produtos == null || compra.produtos.length == 0">Nenhum produto encontrado.</div>

               <div v-else class="cards-1">
                  <div class="product__item product__electronic-item" v-for="(p, index) in compra.produtos" :key="index">
                     <div class="product__thumb position-relative">
                        <a href="javascript:;" class="w-img">
                           <img :src="p.foto != null ? p.foto : ''" alt="product" @error="imageError" @click="viewPhoto">
                        </a>
                        <div class="product__offer" v-if="p.semEstoque">
                           <i class="far fa-box normal text-danger"></i>
                        </div>
                     </div>
                     <div class="product__content">
                        <h6 class="product-name">{{ p.nome }}</h6>
                        <span class="price">
                           <i class="far fa-coins text-warning font-12 me-1"></i> {{ parseInt(parseFloat(p.preco) * global.ecommerce.multiplyCoin) }}
                        </span>
                     </div>
                  </div>
               </div>
            </div>
            <div class="modal-footer">
               <button type="button" class="btn btn-danger btn-simple waves-effect" data-dismiss="modal" @click="compra = null;">Fechar</button>
            </div>
         </div>
      </div>
   </div>
</div>

<script>
   var local = new Vue({
      el: '#local',
      data: {
         compra: null,
         resultado: []
      }
   })

   getPurchases().then((data) => {
      local.resultado = data
   })

   function configPurchase(compra) {
      local.compra = compra
      $('#modalCompra').modal('show')
   }
</script>