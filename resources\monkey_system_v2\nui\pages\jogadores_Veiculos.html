<div id="local" class="card">
   <div class="body p-3 pt-2 mx-1 mb-1">
      <label class="form-label"><i class="far fa-car-side text-primary me-1"></i> Veículo</label>
      <input type="text" class="form-control searchInput" v-model="add.searchVehicle" placeholder="Pesquisar..." />
   </div>

   <div id="searchVehicle" class="cards">
      <div class="body text-center p-2 cursor-pointer delimitarTexto weight-700 font-14" v-for="(veiculo, index) in global.veiculos" :key="index" @click="configAddVehicle(veiculo)">
         <img class="lazyload mb-2" :src="global.urlFotoVeiculos + veiculo +'.png'" @error="imageError" /><br>
         {{ veiculo }}
      </div>
   </div>

   <div v-if="global.veiculos == null || global.veiculos.length == 0">Nenhum veiculo encontrado.</div>
</div>

<script>
   var local = new Vue({
      el: '#local',
      data: {
         add: {'searchVehicle': ''}
      },
      watch: {
         'add.searchVehicle' : function (val) {
            var value = val.toLowerCase()

            $("#searchVehicle .body").filter(function () {
               $(this).toggle($(this).text().toLowerCase().indexOf(value) > -1)
            })
         }
      }
   })

   getVehicles().then((data) => {
      lazyload()

      setTimeout(() => {
         $(".searchInput").focus()
      }, 300);
   })

   function configAddVehicle(vehicle) {
      Swal.fire({
         icon: 'warning',
         title: 'Adicionar veículo',
         text: 'Informe a identidade',
         input: 'text',
         inputPlaceholder: 'Identidade',
         showCancelButton: true,
         cancelButtonText: 'Cancelar',
         confirmButtonText: 'Confirmar',
         inputValidator: (value) => {
            if (!$.isNumeric(value)) {
               return 'Identidade inválida!'
            }
         }
      }).then((result) => {
         if (result.isConfirmed) {
            setVehicleUser(parseInt(result.value), vehicle)
         }
      })
   }
   
</script>