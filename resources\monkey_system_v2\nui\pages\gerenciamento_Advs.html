
<div id="local">
   <div class="card">
      <div class="body p-3 mx-1">
         <div class="row">
            <div class="col-md-4 col-6">
               <label class="form-label"><i class="far fa-stream text-primary me-1"></i> Tipo de pesquisa</label>
               <select class="form-control" v-model="pesquisa.tipo">
                  <option>Identidade</option>
                  <option>Status</option>
               </select>
            </div>
            <div class="col-md-4 col-6" :class="pesquisa.tipo == 'Identidade' ? '' : 'd-none'">
               <label class="form-label"><i class="far fa-hashtag text-primary me-1"></i> Identidade</label>
               <input type="text" class="form-control" v-model="pesquisa.id" @keyup.enter="configAdvs" />
            </div>
            <div class="col-md-4 col-6" :class="pesquisa.tipo == 'Status' ? '' : 'd-none'">
               <label class="form-label"><i class="far fa-calendar text-primary me-1"></i> Status</label>
               <select class="form-control shadow-none" v-model="pesquisa.status">
                  <option>Não expirado</option>
                  <option>Expirado</option>
               </select>
            </div>
            <div class="col-md-2 col-6 align-self-end mt-md-0 mt-2">
               <button class="btn btn-primary w-100 m-0" @click="configAdvs">
                  <i class="fas fa-search"></i>
               </button>
            </div>
            <div class="col-md-2 col-6 align-self-end mt-md-0 mt-2">
               <button class="btn btn-primary w-100 m-0 delimitarTexto" @click="showAddAdv">
                  <i class="fas fa-plus me-2"></i><span class="d-none d-xl-inline">Adicionar</span>
               </button>
            </div>
         </div>
      </div>
   </div>

   <!-- Lista de advertencias -->
   <div class="cards-2">
      <div class="card mb-0" v-for="(a, index) in resultado" :key="index">
         <div class="body text-center">
            <img :src="a.photo" alt="Prova" class="cursor-pointer mb-2" @error="imageErrorScreen" @click="viewPhoto">
            <h5 class="mb-2">{{ a.description }}</h5>
            <p class="text-muted m-b-0">
               <i class="far fa-calendar normal text-primary font-13 me-2"></i><small>Data:</small> {{ new Date(parseInt(a.date)).toLocaleDateString('pt-BR') }}
            </p>
            <p class="text-muted m-b-0"> 
               <i class="far fa-calendar-times normal text-primary font-13 me-2"></i><small>Validade:</small> {{ a.validity }} dias
            </p>
         </div>
      </div>
   </div>
   <div v-if="resultado == null || resultado.length == 0">Nenhuma advertência encontrada.</div>

   <!-- Modal adicionar -->
   <div class="modal fade" id="modalAddAdv" role="dialog">
      <div class="modal-dialog modal-md" role="document">
         <div class="modal-content">
            <div class="modal-header">
               <h4 class="title" id="modalAddAdvLabel">Adicionar advertência</h4>
            </div>
            <div class="modal-body py-3">
               <div class="card">
                  <div class="body">
                     <div class="row">
                        <div class="col">
                           <label class="form-label"><i class="far fa-image normal me-1"></i> Prova</label>
                           <input type="text" class="form-control" v-model="global.screenshot" />
                        </div>
                        <div class="w-max-content align-self-end" @click="takePhoto">
                           <div class="btn btn-primary m-0">
                              <i class="fas fa-camera  normal"></i>
                           </div>
                        </div>
                        <div class="col-12 my-2">
                           <label class="form-label"><i class="far fa-hashtag normal me-1"></i> Identidade</label>
                           <input type="text" class="form-control" v-model="add.id" />
                        </div>
                        <div class="col-12 mb-2">
                           <label class="form-label"><i class="fas fa-ellipsis-h normal me-1"></i> Descrição</label>
                           <textarea class="form-control" v-model="add.descricao" maxlength="100"></textarea>
                        </div>
                        <div class="col-12">
                           <label class="form-label"><i class="far fa-calendar-times normal me-1"></i> Dias p/ expiração</label>
                           <input type="text" class="form-control" v-model="add.expiracao" />
                        </div>
                     </div>
                  </div>
               </div>
            </div>
            <div class="modal-footer">
               <button type="button" class="btn btn-success waves-effect me-2" @click="configAddAdv">
                  <i class="far fa-check me-2"></i>Salvar
               </button>
               <button type="button" class="btn btn-danger btn-simple waves-effect" data-dismiss="modal">Fechar</button>
            </div>
         </div>
      </div>
   </div>
</div>

<script>
   var local = new Vue({
      el: '#local',
      data: {
         add: {'id': '', 'descricao': '', 'expiracao': ''},
         pesquisa: {'tipo': 'Status', 'id': '', 'status': 'Não expirado'},
         resultado: []
      }
   })

   configAdvs()

   setTimeout(() => {
      $('select').selectpicker();

      if (global.screenshot != null) {
         $('#modalAddAdv').modal('show')
      }
   }, 300);

   function showAddAdv() {
      $('#modalAddAdv').modal('show')
   }

   function configAddAdv() {
      addAdvUser(local.add).then((data) => {
         local.add = {'id': '', 'descricao': '', 'expiracao': ''}

         $('#modalAddAdv').modal('hide')
      })
   }

   function configAdvs() {
      let body = {'type': local.pesquisa.tipo, 'value': null}

      switch (local.pesquisa.tipo) {
         case "Identidade":
            body.value = String(local.pesquisa.id).trim()
         break;
         case "Status":
            body.value = String(local.pesquisa.status).trim()
         break;
      }

      searchAdvs(body).then((data) => {
         local.resultado = data
      })
   }
</script>