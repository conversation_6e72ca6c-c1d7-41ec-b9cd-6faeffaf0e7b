<div id="local">
   <div class="cards-2">
      <div class="card mb-0" v-for="(a, index) in advs" :key="index">
         <div class="body text-center">
            <img :src="a.photo" alt="Prova" class="cursor-pointer mb-2" @error="imageErrorScreen" @click="viewPhoto">
            <h5 class="mb-2">{{ a.description }}</h5>
            <p class="text-muted m-b-0"> 
               <i class="far fa-calendar normal text-primary font-13 me-2"></i><small>Data:</small> {{ new Date(parseInt(a.date)).toLocaleDateString('pt-BR') }}
            </p>
            <p class="text-muted m-b-0"> 
               <i class="far fa-calendar-times normal text-primary font-13 me-2"></i><small>Validade:</small> {{ a.validity }} dias
            </p>
         </div>
      </div>
   </div>
   <div v-if="advs == null || advs.length == 0">Nenhuma advertência encontrada.</div>
</div>

<script>
   var local = new Vue({
      el: '#local',
      data: {
         advs: []
      }
   })

   getUserAdvs().then((data) => {
      local.advs = data
   })
</script>