
<div id="local">
   <div class="card">
      <div class="body p-3">
         <div class="row">
            <div class="col-md-8">
               <label class="form-label"><i class="far fa-hashtag text-primary me-1"></i> Identidade</label>
               <input type="text" class="form-control" v-model="pesquisa.id" @keyup.enter="configUser" />
            </div>
            <div class="col-md-2 col-6 align-self-end mt-md-0 mt-2">
               <button class="btn btn-primary w-100 m-0" @click="configUser">
                  <i class="fas fa-search"></i>
               </button>
            </div>
            <div class="col-md-2 col-6 align-self-end mt-md-0 mt-2">
               <button class="btn btn-primary w-100 m-0 delimitarTexto" @click="configAddPerm">
                  <i class="fas fa-plus me-2"></i><span class="d-none d-xl-inline">Adicionar</span>
               </button>
            </div>
         </div>
      </div>
   </div>

   <div class="row">
      <div class="col-12 px-0" v-for="(permissao, index) in resultado" :key="index">
         <div class="card">
            <div class="body px-3 py-10">
               <div class="row">
                  <div class="col">
                     <h5 class="delimitarTexto font-17 m-0">{{ permissao }}</h5>
                  </div>
                  <div class="w-max-content">
                     <a class="w-100 text-dark" @click="configRemovePerm(permissao, index)" href="javascript:;">
                        <i class="far fa-trash font-16 normal text-danger"></i>
                     </a>
                  </div>
               </div>
            </div>
         </div>
      </div>
   </div>
   <div v-if="resultado == null || resultado.length == 0">Nenhuma permissão encontrada.</div>
</div>

<script>
   var local = new Vue({
      el: '#local',
      data: {
         pesquisa: {'id': ''},
         resultado: []
      }
   })

   getPermissions().then((data) => {
      setTimeout(() => {
         $(".searchInput").focus()
      }, 300);
   })

   function configAddPerm() {
      if (local.pesquisa.id == null || String(local.pesquisa.id).trim().length == 0) {
         Toast.fire({
            title: 'Identidade inválida!',
            icon: 'error'
         })

         return
      }

      Swal.fire({
         icon: 'question',
         title: 'Adicionar permissão',
         text: 'Informe a permissão',
         input: 'text',
         inputPlaceholder: 'Pesquisar ou enter para adicionar...',
         showCancelButton: true,
         cancelButtonText: 'Cancelar',
         confirmButtonText: 'Confirmar',
         inputValidator: (value) => {
            if (value == null || String(value).trim().length < 3) {
               return 'Digite ao menos 3 caracteres!'
            }
         },
         customClass: {
            input: 'searchInput'
         },
         didOpen: function() {
            setTimeout(() => {
               $('.searchInput').autocomplete({
                  source: global.permissoes,
                  minLength: 3
               });
            }, 300);
         }
      }).then((result) => {
         if (result.isConfirmed) {
            setPermissionUser(local.pesquisa.id, String(result.value).trim()).then((data) => {
               local.resultado.push(String(result.value).trim())
            })
         }
      })
   }
      
   function configRemovePerm(perm, index) {
      removePermissionUser(local.pesquisa.id, perm).then((data) => {
         local.resultado.splice(index, 1)
      })
   }

   function configUser() {
      getInfoUser(local.pesquisa.id).then((data) => {
         local.resultado = data.permissoes
      })
   }

</script>