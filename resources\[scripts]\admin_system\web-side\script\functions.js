var global = new Vue({
   el: '#app',
   data: {
      userLogado: {},
      usersOnline: {},
      permissao: {'NV1': false, 'NV2': false, 'NV3': false, 'NV4': false},
      page: {'url': 'pages/dashboard.html', 'titulo': 'Dashboard', 'menu': 'Principal'},
      errorImg: 'https://cdn.discordapp.com/attachments/795099022439481390/814929467863859221/sem_foto.png',
      errorImgScreen: 'https://cdn.discordapp.com/attachments/795099022439481390/1009472987411521546/sem_foto.png',
      urlLogo: "https://media.discordapp.net/attachments/956302650750673036/956302746531819580/creativbe.png",
      urlFotoVeiculos: 'https://j4v4.site/MC/carros/icons/',
      urlFotoItens: 'https://j4v4.site/MC/itens/icons/',
      ecommerce: {'url': 'http://**************:8085/Monkey-EcommerceXX/', 'categorias': [], 'multiplyCoin': 1},
      permissoes: [],
      itens: [],
      veiculos: [],
      vips: [],
      screenshot: null
   }
})

var Toast;

$.getScript("https://cdn.jsdelivr.net/npm/sweetalert2@11.4.26/dist/sweetalert2.all.min.js", function() {
   Toast = Swal.mixin({
      toast: true,
      position: 'bottom-end',
      showConfirmButton: false,
      timer: 3000,
      timerProgressBar: true,
      didOpen: (toast) => {
         toast.addEventListener('mouseenter', Swal.stopTimer)
         toast.addEventListener('mouseleave', Swal.resumeTimer)
      }
   })
});

document.addEventListener("DOMContentLoaded", function() {
	window.addEventListener("message", function (event) {
      switch (event.data.method) {
         case "open":
            document.getElementById('app').style.display = 'block'
            toggleLoading(false)
            // getCategorias()
            getOnline()
      
            $("#content-body").load(global.page.url);
         break;
         case "close":
            document.getElementById('app').style.display = 'none'
         break;
         case "sendUrl":
            global.screenshot = event.data.URL
         break;
      }
   })

   window.onkeyup = function(data) {
      if (data.which == 27){
         close()
      }
   }
})

function takePhoto() {
   return new Promise(function(resolve, reject) {
      fetch('http://monkey_system_v2/takePhoto', { method: 'POST' })
      .then(function() {
         resolve()
         
      }).catch(function() {
         reject()
      });
   });
}

function clearAdHistory() {
   return new Promise(function(resolve, reject) {
      localStorage.removeItem('meusAnuncios')
   
      Toast.fire({
         icon: 'success',
         title: 'Histórico limpo!'
      });

      resolve()
   })
}

function sendAd(ad) {
   return new Promise(function(resolve, reject) {
      if (ad.value == null || String(ad.value).trim().length == 0) {
         Toast.fire({
            title: ad.type +' inválida!',
            icon: 'error'
         })

         reject()
         return
      } else if (ad.message == null || String(ad.message).trim().length == 0) {
         Toast.fire({
            title: 'Mensagem inválida!',
            icon: 'error'
         })

         reject()
         return
      }

      Swal.fire({
         icon: 'warning',
         title: 'Enviar anúncio',
         showCancelButton: true,
         cancelButtonText: 'Cancelar',
         confirmButtonText: 'Confirmar'

      }).then((result) => {
         if (result.isConfirmed) {
            toggleLoading(true)

            fetch('http://monkey_system_v2/sendAd', { 
               headers: { "Content-Type": "application/json" },
               method: "POST",
               body: JSON.stringify({
                  type: ad.type,
                  value: ad.value,
                  message: ad.message
               })
            }).then(function() {
               Toast.fire({
                  title: 'Anúncio enviado!',
                  icon: 'success'
               })

               toggleLoading(false)
               resolve()

            }).catch(function() {
               Toast.fire({
                  title: 'Erro ao enviar!',
                  icon: 'error'
               })

               toggleLoading(false)
               reject()
            });
         }
      })
   })
}

function viewPhoto(e) {
   Swal.fire({
      imageUrl: e.target.src,
      imageHeight: 350,
      showConfirmButton: false,
      showCancelButton: true,
      cancelButtonText: 'Fechar'
   });
}

function changePage(titulo, menu, page) {
   if (global.page.url != page || global.page.menu != menu || global.page.titulo != titulo) {
      toggleLoading(true)

      $("#content-body").load(page, function(response, status, xhr) {
         if (status != "error") {
            global.page.url = page
            global.page.titulo = titulo
            global.page.menu = menu
         }

         toggleLoading(false)
      });
   }
}

function toggleLoading(show) {
   if (show) {
      $(".loadingContent").addClass('d-block')
   } else {
      $(".loadingContent").removeClass('d-block')
   }
}

function close() {
   return new Promise(function(resolve, reject) {
      fetch('http://monkey_system_v2/close', { method: 'POST' })
      .then(function() {
         document.getElementById('app').style.display = 'none'
         resolve()
         
      }).catch(function() {
         reject()
      });
   });
}

function getInfo() {
   return new Promise(function(resolve, reject) {
      toggleLoading(true)

      return fetch('http://monkey_system_v2/getInfo', { method: 'POST' })
      .then(function(response) {
         return response.json();
      })
      .then(function(data) {
         let retorno = {}
         retorno.banco = data.jogadorBanco;
         retorno.coins = data.jogadorCoins;
         retorno.id = data.jogadorId;
         retorno.nome = data.jogadorNome;
         retorno.telefone = data.jogadorTelefone;
         retorno.rg = data.jogadorRG;
         retorno.garagens = data.jogadorGaragens;
         retorno.permissoes = [];

         data.jogadorPermissoes.forEach(p => {
            if (!retorno.permissoes.includes(p.permiss)) {
               retorno.permissoes.push(p.permiss);
            }
         });

         global.permissao = {
            'NV1': retorno.permissoes.some(x => x.toLowerCase() == 'admin'), 
            'NV2': retorno.permissoes.some(x => x.toLowerCase() == 'suporte'), 
            'NV3': retorno.permissoes.some(x => x.toLowerCase() == 'staff'), 
            'NV4': retorno.permissoes.some(x => x.toLowerCase() == 'whitelist')
         }

         global.userLogado = retorno;

         toggleLoading(false)
         resolve(retorno)
         
      }).catch(function() {
         toggleLoading(false)
         reject()
      });
   });
}

function getUser() {
   return new Promise(function(resolve, reject) {
      toggleLoading(true)

      return fetch('http://monkey_system_v2/getUser', { method: 'POST' })
      .then(function(response) {
         return response.json();
      })
      .then(function(data) {
         toggleLoading(false)
         resolve(data)

      }).catch(function() {
         toggleLoading(false)
         reject()
      });
   });
}

// function getCategorias() {
//    return new Promise(function(resolve, reject) {
//       toggleLoading(true)

//       return fetch(global.ecommerce.url +'product/getcategorias', { method: 'GET' })
//       .then(function(response) {
//          return response.json();
//       })
//       .then(function(data) {
//          global.ecommerce.categorias = data
//          toggleLoading(false)
//          resolve()

//       }).catch(function() {
//          toggleLoading(false)
//          reject()
//       });
//    });
// }

function getProducts() {
   return new Promise(function(resolve, reject) {
      toggleLoading(true)

      return fetch(global.ecommerce.url +'product/search?categoria='+ global.page.titulo +'&nomeProduto=&subcategoria=Todas+subcategorias', { method: 'GET' })
      .then(function(response) {
         return response.json();
      })
      .then(function(data) {
         toggleLoading(false)
         resolve(data)

      }).catch(function() {
         toggleLoading(false)
         reject()
      });
   });
}

function getOnline() {
   return new Promise(function(resolve, reject) {
      toggleLoading(true)

      return fetch('http://monkey_system_v2/getOnline', { method: 'POST' })
      .then(function(response) {
         return response.json();
      })
      .then(function(data) {
         global.usersOnline = data
         toggleLoading(false)
         resolve()

      }).catch(function() {
         toggleLoading(false)
         reject()
      });
   });
}

function getInfoUser(id) {
   return new Promise(function(resolve, reject) {
      if (id == null || String(id).trim().length == 0) {
         Toast.fire({
            title: 'Identidade inválida!',
            icon: 'error'
         })

         reject()
         return
      }

      toggleLoading(true)

      return fetch('http://monkey_system_v2/getInfoUser', { 
         headers: { "Content-Type": "application/json" },
         method: "POST",
         body: JSON.stringify({
            id: id
         })
      })
      .then(function(response) {
         return response.json();
      })
      .then(function(data) {
         toggleLoading(false)
         resolve(data)

      }).catch(function() {
         toggleLoading(false)
         reject()
      });
   });
}

function searchVips(vip) {
   return new Promise(function(resolve, reject) {
      if (vip == null || String(vip).trim().length == 0) {
         Toast.fire({
            title: 'VIP inválido!',
            icon: 'error'
         })

         reject()
         return
      }

      toggleLoading(true)

      return fetch('http://monkey_system_v2/searchVips', { 
         headers: { "Content-Type": "application/json" },
         method: "POST",
         body: JSON.stringify({
            vip: vip
         })
      })
      .then(function(response) {
         return response.json();
      })
      .then(function(data) {
         toggleLoading(false)
         resolve(data)

      }).catch(function() {
         toggleLoading(false)
         reject()
      });
   });
}

function getUserAdvs() {
   return new Promise(function(resolve, reject) {
      toggleLoading(true)

      return fetch('http://monkey_system_v2/getUserAdvs', { method: 'POST' })
      .then(function(response) {
         return response.json();
      })
      .then(function(data) {
         toggleLoading(false)
         resolve(data)

      }).catch(function() {
         toggleLoading(false)
         reject()
      });
   });
}

function getUserTickets() {
   return new Promise(function(resolve, reject) {
      toggleLoading(true)

      return fetch('http://monkey_system_v2/getUserTickets', { method: 'POST' })
      .then(function(response) {
         return response.json();
      })
      .then(function(data) {
         toggleLoading(false)
         resolve(data)

      }).catch(function() {
         toggleLoading(false)
         reject()
      });
   });
}

function getPermissions() {
   return new Promise(function(resolve, reject) {
      if (global.permissoes.length > 0) {
         resolve()
         return
      }

      return fetch('http://monkey_system_v2/getPermissions', { method: 'POST' })
      .then(function(response) {
         return response.json();
      })
      .then(function(data) {
         global.permissoes = data
         resolve()

      }).catch(function() {
         toggleLoading(false)
         reject()
      });
   });
}

function getVips() {
   return new Promise(function(resolve, reject) {
      if (global.vips.length > 0) {
         resolve()
         return
      }

      return fetch('http://monkey_system_v2/getVips', { method: 'POST' })
      .then(function(response) {
         return response.json();
      })
      .then(function(data) {
         global.vips = data
         resolve()

      }).catch(function() {
         toggleLoading(false)
         reject()
      });
   });
}

function getItems() {
   return new Promise(function(resolve, reject) {
      if (global.itens.length > 0) {
         resolve()
         return
      }

      return fetch('http://monkey_system_v2/getItems', { method: 'POST' })
      .then(function(response) {
         return response.json();
      })
      .then(function(data) {
         global.itens = data
         resolve()

      }).catch(function() {
         toggleLoading(false)
         reject()
      });
   });
}

function getVehicles() {
   return new Promise(function(resolve, reject) {
      if (global.veiculos.length > 0) {
         resolve()
         return
      }
      
      return fetch('http://monkey_system_v2/getVehicles', { method: 'POST' })
      .then(function(response) {
         return response.json();
      })
      .then(function(data) {
         global.veiculos = data
         resolve()

      }).catch(function() {
         toggleLoading(false)
         reject()
      });
   });
}

function searchUsers(pesquisa) {
   return new Promise(function(resolve, reject) {
      switch (pesquisa.type) {
         case "Identidade":
            if (String(pesquisa.value).trim().length == 0) {
               Toast.fire({
                  title: pesquisa.type +' inválido!',
                  icon: 'error'
               })
   
               reject()
               return
            }
         break;
         case "Steam":
            if (String(pesquisa.value).trim().length < 10) {
               Toast.fire({
                  title: pesquisa.type +' inválido!',
                  icon: 'error'
               })
   
               reject()
               return
            }
         break;
         case "Permissão":
            if (String(pesquisa.value).trim().length < 4) {
               Toast.fire({
                  title: pesquisa.type +' inválido!',
                  icon: 'error'
               })
   
               reject()
               return
            }
         break;
         case "Whitelist":
            if (String(pesquisa.value).trim().length < 7) {
               Toast.fire({
                  title: pesquisa.type +' inválido!',
                  icon: 'error'
               })
   
               reject()
               return
            }
         break;
         case "Online":
            if (String(pesquisa.value).trim().length < 4) {
               Toast.fire({
                  title: pesquisa.type +' inválido!',
                  icon: 'error'
               })
   
               reject()
               return
            }
         break;
         case "Código de aprovação":
            if (String(pesquisa.value).trim().length == 0) {
               Toast.fire({
                  title: pesquisa.type +' inválido!',
                  icon: 'error'
               })
   
               reject()
               return
            }
         break;
      }
   
      fetch('http://monkey_system_v2/searchUsers', {
         headers: { "Content-Type": "application/json" },
         method: "POST",
         body: JSON.stringify(pesquisa)
      })
      .then(function(response) {
         return response.json();
      })
      .then(function(data) {
         toggleLoading(false)
         resolve(data)
   
      }).catch(function() {
         toggleLoading(false)
         reject()
      });
   })
}

function searchAdvs(pesquisa) {
   return new Promise(function(resolve, reject) {
      switch (pesquisa.type) {
         case "Identidade":
            if (String(pesquisa.value).trim().length == 0) {
               Toast.fire({
                  title: pesquisa.type +' inválido!',
                  icon: 'error'
               })
   
               reject()
               return
            }
         break;
         case "Status":
            if (String(pesquisa.value).trim().length < 5) {
               Toast.fire({
                  title: pesquisa.type +' inválido!',
                  icon: 'error'
               })
   
               reject()
               return
            }
         break;
      }
   
      fetch('http://monkey_system_v2/searchAdvs', {
         headers: { "Content-Type": "application/json" },
         method: "POST",
         body: JSON.stringify(pesquisa)
      })
      .then(function(response) {
         return response.json();
      })
      .then(function(data) {
         toggleLoading(false)
         resolve(data)
   
      }).catch(function() {
         toggleLoading(false)
         reject()
      });
   })
}

function changeMoney(id) {
   return new Promise(function(resolve, reject) {
      Swal.fire({
         icon: 'warning',
         title: 'Alterar banco',
         text: 'Informe a quantidade',
         input: 'text',
         inputPlaceholder: 'Quantidade',
         showCancelButton: true,
         cancelButtonText: 'Cancelar',
         confirmButtonText: 'Confirmar',
         inputValidator: (value) => {
            if (!$.isNumeric(value)) {
               return 'Valor inválido!'
            }
         }
      }).then((result) => {
         if (result.isConfirmed) {
            toggleLoading(true)

            fetch('http://monkey_system_v2/changeMoney', { 
               headers: { "Content-Type": "application/json" },
               method: "POST",
               body: JSON.stringify({
                  id: id,
                  amount: parseInt(result.value)
               })
            }).then(function() {
               Toast.fire({
                  title: 'Alteração salva!',
                  icon: 'success'
               })

               toggleLoading(false)
               resolve(parseInt(result.value))

            }).catch(function() {
               Toast.fire({
                  title: 'Erro ao salvar!',
                  icon: 'error'
               })

               toggleLoading(false)
               reject()
            });
         }
      })
   })
}

function changeCoins(id) {
   return new Promise(function(resolve, reject) {
      Swal.fire({
         icon: 'warning',
         title: 'Alterar coins',
         text: 'Informe a quantidade',
         input: 'text',
         inputPlaceholder: 'Quantidade',
         showCancelButton: true,
         cancelButtonText: 'Cancelar',
         confirmButtonText: 'Confirmar',
         inputValidator: (value) => {
            if (!$.isNumeric(value)) {
               return 'Valor inválido!'
            }
         }
      }).then((result) => {
         if (result.isConfirmed) {
            toggleLoading(true)

            fetch('http://monkey_system_v2/changeCoins', { 
               headers: { "Content-Type": "application/json" },
               method: "POST",
               body: JSON.stringify({
                  id: id,
                  amount: parseInt(result.value)
               })
            }).then(function() {
               Toast.fire({
                  title: 'Alteração salva!',
                  icon: 'success'
               })
               
               toggleLoading(false)
               resolve(parseInt(result.value))

            }).catch(function() {
               Toast.fire({
                  title: 'Erro ao salvar!',
                  icon: 'error'
               })

               toggleLoading(false)
               reject()
            });
         }
      })
   })
}

function changeCharacters(id) {
   return new Promise(function(resolve, reject) {
      Swal.fire({
         icon: 'warning',
         title: 'Alterar número de personagens',
         text: 'Informe a quantidade',
         input: 'text',
         inputPlaceholder: 'Quantidade',
         showCancelButton: true,
         cancelButtonText: 'Cancelar',
         confirmButtonText: 'Confirmar',
         inputValidator: (value) => {
            if (!$.isNumeric(value)) {
               return 'Valor inválido!'
            }
         }
      }).then((result) => {
         if (result.isConfirmed) {
            toggleLoading(true)

            fetch('http://monkey_system_v2/changeCharacters', { 
               headers: { "Content-Type": "application/json" },
               method: "POST",
               body: JSON.stringify({
                  id: id,
                  amount: parseInt(result.value)
               })
            }).then(function() {
               Toast.fire({
                  title: 'Alteração salva!',
                  icon: 'success'
               })
               
               toggleLoading(false)
               resolve(parseInt(result.value))

            }).catch(function() {
               Toast.fire({
                  title: 'Erro ao salvar!',
                  icon: 'error'
               })

               toggleLoading(false)
               reject()
            });
         }
      })
   })
}

function changeParkingSpaces(id) {
   return new Promise(function(resolve, reject) {
      Swal.fire({
         icon: 'warning',
         title: 'Alterar vagas de garagem',
         text: 'Informe a quantidade',
         input: 'text',
         inputPlaceholder: 'Quantidade',
         showCancelButton: true,
         cancelButtonText: 'Cancelar',
         confirmButtonText: 'Confirmar',
         inputValidator: (value) => {
            if (!$.isNumeric(value)) {
               return 'Valor inválido!'
            }
         }
      }).then((result) => {
         if (result.isConfirmed) {
            toggleLoading(true)

            fetch('http://monkey_system_v2/changeParkingSpaces', { 
               headers: { "Content-Type": "application/json" },
               method: "POST",
               body: JSON.stringify({
                  id: id,
                  amount: parseInt(result.value)
               })
            }).then(function() {
               Toast.fire({
                  title: 'Alteração salva!',
                  icon: 'success'
               })
               
               toggleLoading(false)
               resolve(parseInt(result.value))

            }).catch(function() {
               Toast.fire({
                  title: 'Erro ao salvar!',
                  icon: 'error'
               })

               toggleLoading(false)
               reject()
            });
         }
      })
   })
}

function toggleWhitelist(steam, isWhitelist) {
   return new Promise(function(resolve, reject) {
      Swal.fire({
         icon: 'warning',
         title: (isWhitelist ? 'Retirar' : 'Adicionar') +' whitelist',
         text: 'Tem certeza?',
         showCancelButton: true,
         cancelButtonText: 'Cancelar',
         confirmButtonText: 'Confirmar'

      }).then((result) => {
         if (result.isConfirmed) {
            toggleLoading(true)

            fetch('http://monkey_system_v2/toggleWhitelist', { 
               headers: { "Content-Type": "application/json" },
               method: "POST",
               body: JSON.stringify({
                  steam: String(steam).replace('steam:', '')
               })
            }).then(function() {
               Toast.fire({
                  title: 'Alteração salva!',
                  icon: 'success'
               })
               
               toggleLoading(false)
               resolve()

            }).catch(function() {
               Toast.fire({
                  title: 'Erro ao salvar!',
                  icon: 'error'
               })

               toggleLoading(false)
               reject()
            });
         }
      })
   })
}

function toggleBan(id, isBanned) {
   return new Promise(function(resolve, reject) {
      if (isBanned) {
         Swal.fire({
            icon: 'warning',
            title: 'Retirar banimento',
            confirmButtonText: 'Aplicar',
            showCancelButton: true,
            cancelButtonText: 'Cancelar'

         }).then((result) => {
            if (result.isConfirmed) {
               toggleLoading(true)
   
               fetch('http://monkey_system_v2/toggleBan', { 
                  headers: { "Content-Type": "application/json" },
                  method: "POST",
                  body: JSON.stringify({
                     id: id,
                     time: 0
                  })
               }).then(function() {
                  Toast.fire({
                     title: 'Alteração salva!',
                     icon: 'success'
                  })
                  
                  toggleLoading(false)
                  resolve()
   
               }).catch(function() {
                  Toast.fire({
                     title: 'Erro ao salvar!',
                     icon: 'error'
                  })
   
                  toggleLoading(false)
                  reject()
               });
            }
         })
      } else {
         Swal.fire({
            icon: 'warning',
            title: 'Aplicar banimento',
            text: 'Informe o tempo (Dias)',
            input: 'text',
            inputPlaceholder: 'Dias',
            confirmButtonText: 'Aplicar',
            showCancelButton: true,
            cancelButtonText: 'Cancelar',
            showDenyButton: true,
            denyButtonText: 'Permanente',
            inputValidator: (value) => {
               if (!$.isNumeric(value)) {
                  return 'Tempo inválido!'
               }
            }
         }).then((result) => {
            if (!result.isDismissed) {
               toggleLoading(true)
   
               fetch('http://monkey_system_v2/toggleBan', { 
                  headers: { "Content-Type": "application/json" },
                  method: "POST",
                  body: JSON.stringify({
                     id: id,
                     time: (result.isConfirmed ? result.value : 0)
                  })
               }).then(function() {
                  Toast.fire({
                     title: 'Alteração salva!',
                     icon: 'success'
                  })
                  
                  toggleLoading(false)
                  resolve()
   
               }).catch(function() {
                  Toast.fire({
                     title: 'Erro ao salvar!',
                     icon: 'error'
                  })
   
                  toggleLoading(false)
                  reject()
               });
            }
         })
      }
   })
}

function tptoUser(id) {
   return new Promise(function(resolve, reject) {
      Swal.fire({
         icon: 'warning',
         title: 'Teleportar',
         text: 'Tem certeza?',
         showCancelButton: true,
         cancelButtonText: 'Cancelar',
         confirmButtonText: 'Confirmar'

      }).then((result) => {
         if (result.isConfirmed) {
            toggleLoading(true)

            fetch('http://monkey_system_v2/tptoUser', { 
               headers: { "Content-Type": "application/json" },
               method: "POST",
               body: JSON.stringify({
                  id: id
               })
            }).then(function() {
               toggleLoading(false)
               resolve()

            }).catch(function() {
               toggleLoading(false)
               reject()
            });
         }
      })
   })
}

function goodUser(id) {
   return new Promise(function(resolve, reject) {
      Swal.fire({
         icon: 'warning',
         title: 'Regenerar',
         text: 'Tem certeza?',
         showCancelButton: true,
         cancelButtonText: 'Cancelar',
         confirmButtonText: 'Confirmar'

      }).then((result) => {
         if (result.isConfirmed) {
            toggleLoading(true)

            fetch('http://monkey_system_v2/goodUser', { 
               headers: { "Content-Type": "application/json" },
               method: "POST",
               body: JSON.stringify({
                  id: id
               })
            }).then(function() {
               Toast.fire({
                  title: 'Vida regenerada!',
                  icon: 'success'
               })

               toggleLoading(false)
               resolve()

            }).catch(function() {
               Toast.fire({
                  title: 'Erro ao regenerar!',
                  icon: 'error'
               })

               toggleLoading(false)
               reject()
            });
         }
      })
   })
}

function removeItemUser(id, item) {
   return new Promise(function(resolve, reject) {
      Swal.fire({
         icon: 'warning',
         title: 'Remover item',
         text: 'Informe a quantidade',
         input: 'text',
         inputPlaceholder: 'Quantidade',
         confirmButtonText: 'Confirmar',
         showCancelButton: true,
         cancelButtonText: 'Tudo',
         showDenyButton: true,
         denyButtonText: 'Cancelar',
         inputValidator: (value) => {
            if (!$.isNumeric(value)) {
               return 'Quantidade inválida!'
            }
         }
      }).then((result) => {
         if (!result.isDenied) {
            toggleLoading(true)

            fetch('http://monkey_system_v2/removeItemUser', { 
               headers: { "Content-Type": "application/json" },
               method: "POST",
               body: JSON.stringify({
                  id: id,
                  item: item,
                  amount: (result.isConfirmed ? parseInt(result.value) : 0)
               })
            }).then(function() {
               Toast.fire({
                  title: 'Item removido!',
                  icon: 'success'
               })

               toggleLoading(false)
               resolve((result.isConfirmed ? parseInt(result.value) : 999999999))

            }).catch(function() {
               Toast.fire({
                  title: 'Erro ao remover!',
                  icon: 'error'
               })

               toggleLoading(false)
               reject()
            });
         }
      });
   });
}

function removeVehicleUser(id, vehicle) {
   return new Promise(function(resolve, reject) {
      Swal.fire({
         icon: 'warning',
         title: 'Remover veículo',
         showCancelButton: true,
         cancelButtonText: 'Cancelar',
         confirmButtonText: 'Confirmar'

      }).then((result) => {
         if (result.isConfirmed) {
            toggleLoading(true)

            fetch('http://monkey_system_v2/removeVehicleUser', { 
               headers: { "Content-Type": "application/json" },
               method: "POST",
               body: JSON.stringify({
                  id: id,
                  vehicle: vehicle
               })
            }).then(function() {
               Toast.fire({
                  title: 'Veículo removido!',
                  icon: 'success'
               })

               toggleLoading(false)
               resolve()

            }).catch(function() {
               Toast.fire({
                  title: 'Erro ao remover!',
                  icon: 'error'
               })
               
               toggleLoading(false)
               reject()
            });
         }
      });
   });
}

function removePermissionUser(id, perm) {
   return new Promise(function(resolve, reject) {
      Swal.fire({
         icon: 'warning',
         title: 'Remover permissão',
         showCancelButton: true,
         cancelButtonText: 'Cancelar',
         confirmButtonText: 'Confirmar'

      }).then((result) => {
         if (result.isConfirmed) {
            toggleLoading(true)

            fetch('http://monkey_system_v2/removePermissionUser', { 
               headers: { "Content-Type": "application/json" },
               method: "POST",
               body: JSON.stringify({
                  id: id,
                  permission: perm
               })
            }).then(function() {
               Toast.fire({
                  title: 'Permissão removida!',
                  icon: 'success'
               })

               toggleLoading(false)
               resolve()

            }).catch(function() {
               Toast.fire({
                  title: 'Erro ao remover!',
                  icon: 'error'
               })
               
               toggleLoading(false)
               reject()
            });
         }
      });
   });
}

function setVehicleUser(id, vehicle) {
   return new Promise(function(resolve, reject) {
      Swal.fire({
         icon: 'warning',
         title: 'Adicionar veículo',
         showCancelButton: true,
         cancelButtonText: 'Cancelar',
         confirmButtonText: 'Confirmar'

      }).then((result) => {
         if (result.isConfirmed) {
            toggleLoading(true)

            fetch('http://monkey_system_v2/setVehicleUser', { 
               headers: { "Content-Type": "application/json" },
               method: "POST",
               body: JSON.stringify({
                  id: id,
                  vehicle: vehicle
               })
            }).then(function() {
               Toast.fire({
                  title: 'Veículo adicionado!',
                  icon: 'success'
               })

               toggleLoading(false)
               resolve()

            }).catch(function() {
               Toast.fire({
                  title: 'Erro ao adicionar!',
                  icon: 'error'
               })
               
               toggleLoading(false)
               reject()
            });
         }
      });
   });
}

function setItemUser(id, item) {
   return new Promise(function(resolve, reject) {
      Swal.fire({
         icon: 'warning',
         title: 'Adicionar item',
         text: 'Informe a quantidade',
         input: 'text',
         inputPlaceholder: 'Quantidade',
         showCancelButton: true,
         cancelButtonText: 'Cancelar',
         confirmButtonText: 'Confirmar',
         inputValidator: (value) => {
            if (!$.isNumeric(value)) {
               return 'Quantidade inválida!'
            }
         }
      }).then((result) => {
         if (result.isConfirmed) {
            toggleLoading(true)

            fetch('http://monkey_system_v2/setItemUser', { 
               headers: { "Content-Type": "application/json" },
               method: "POST",
               body: JSON.stringify({
                  id: id,
                  item: item,
                  amount: parseInt(result.value)
               })
            }).then(function() {
               Toast.fire({
                  title: 'Item adicionado!',
                  icon: 'success'
               })

               toggleLoading(false)
               resolve(parseInt(result.value))

            }).catch(function() {
               Toast.fire({
                  title: 'Erro ao adicionar!',
                  icon: 'error'
               })

               toggleLoading(false)
               reject()
            });
         }
      });
   });
}

function setPermissionUser(id, perm) {
   return new Promise(function(resolve, reject) {
      if (perm == null || String(perm).trim().length == 0) {
         Toast.fire({
            title: 'Permissão inválida!',
            icon: 'error'
         })

         reject()
         return
      }

      Swal.fire({
         icon: 'warning',
         title: 'Adicionar permissão',
         confirmButtonText: 'Confirmar',
         showCancelButton: true,
         cancelButtonText: 'Cancelar'
      }).then((result) => {
         if (result.isConfirmed) {
            toggleLoading(true)

            fetch('http://monkey_system_v2/setPermissionUser', { 
               headers: { "Content-Type": "application/json" },
               method: "POST",
               body: JSON.stringify({
                  id: id,
                  permission: perm
               })
            }).then(function() {
               Toast.fire({
                  title: 'Permissão adicionada!',
                  icon: 'success'
               })

               toggleLoading(false)
               resolve()

            }).catch(function() {
               Toast.fire({
                  title: 'Erro ao adicionar!',
                  icon: 'error'
               })
               
               toggleLoading(false)
               reject()
            });
         }
      })
   });
}

function createTicket() {
   return new Promise(function(resolve, reject) {
      Swal.fire({
         icon: 'warning',
         title: 'Criar ticket',
         text: 'Informe um breve resumo',
         input: 'textarea',
         showCancelButton: true,
         cancelButtonText: 'Cancelar',
         confirmButtonText: 'Confirmar',
         inputValidator: (value) => {
            if (value == null || String(value).trim().length == 0) {
               return 'É necessário informar um breve resumo do ocorrido!'

            } else if (String(value).length > 100) {
               return 'Limite de 100 caracteres excedido!'
            }
         }
      }).then((result) => {
         if (result.isConfirmed) {
            toggleLoading(true)

            fetch('http://monkey_system_v2/createTicket', { 
               headers: { "Content-Type": "application/json" },
               method: "POST",
               body: JSON.stringify({
                  title: result.value
               })
            }).then(function(response) {
               return response.json();

            }).then(function(data) {
               Toast.fire({
                  title: 'Ticket criado!',
                  icon: 'success'
               })

               toggleLoading(false)
               resolve(data)

            }).catch(function() {
               Toast.fire({
                  title: 'Erro ao criar!',
                  icon: 'error'
               })

               toggleLoading(false)
               reject()
            });
         }
      })
   })
}

function getTicket(id) {
   return new Promise(function(resolve, reject) {
      toggleLoading(true)

      return fetch('http://monkey_system_v2/getTicket', { 
         headers: { "Content-Type": "application/json" },
         method: "POST",
         body: JSON.stringify({
            id: id
         })
      })
      .then(function(response) {
         return response.json();
      })
      .then(function(data) {
         toggleLoading(false)
         resolve(data)

      }).catch(function() {
         toggleLoading(false)
         reject()
      });
   });
}

function getTickets() {
   return new Promise(function(resolve, reject) {
      toggleLoading(true)

      return fetch('http://monkey_system_v2/getTickets', { method: 'POST' })
      .then(function(response) {
         return response.json();
      })
      .then(function(data) {
         toggleLoading(false)
         resolve(data)

      }).catch(function() {
         toggleLoading(false)
         reject()
      });
   });
}

function sendMessage(id, message) {
   return new Promise(function(resolve, reject) {
      toggleLoading(true)

      return fetch('http://monkey_system_v2/sendMessage', { 
         headers: { "Content-Type": "application/json" },
         method: "POST",
         body: JSON.stringify({
            id: id,
            message: message
         })
      })
      .then(function(response) {
         return response.json();
      })
      .then(function(data) {
         toggleLoading(false)
         resolve(data)

      }).catch(function() {
         toggleLoading(false)
         reject()
      });
   });
}

function rateTicket(id) {
   return new Promise(function(resolve, reject) {
      Swal.fire({
         icon: 'question',
         title: 'Avaliar atendimento',
         input: 'range',
         inputAttributes: {
            min: 1,
            max: 10,
            step: 1
         },
         inputValue: 5,
         showCancelButton: true,
         cancelButtonText: 'Cancelar',
         confirmButtonText: 'Confirmar'

      }).then((result) => {
         if (result.isConfirmed) {
            toggleLoading(true)
      
            fetch('http://monkey_system_v2/rateTicket', { 
               headers: { "Content-Type": "application/json" },
               method: "POST",
               body: JSON.stringify({
                  id: id,
                  rating: result.value
               })
            })
            .then(function(response) {
               return response.json();
            })
            .then(function() {
               Toast.fire({
                  title: 'Avaliação salva!',
                  icon: 'success'
               })

               toggleLoading(false)
               resolve()
      
            }).catch(function() {
               Toast.fire({
                  title: 'Erro ao avaliar!',
                  icon: 'error'
               })

               toggleLoading(false)
               reject()
            });
         }
      })
   });
}

function changeStatus(id, status) {
   return new Promise(function(resolve, reject) {
      toggleLoading(true)

      return fetch('http://monkey_system_v2/changeStatus', { 
         headers: { "Content-Type": "application/json" },
         method: "POST",
         body: JSON.stringify({
            id: id,
            status: status
         })
      })
      .then(function(response) {
         return response.json();
      })
      .then(function() {
         Toast.fire({
            title: 'Ticket finalizado!',
            icon: 'success'
         })

         toggleLoading(false)
         resolve()

      }).catch(function() {
         Toast.fire({
            title: 'Erro ao finalizar!',
            icon: 'error'
         })

         toggleLoading(false)
         reject()
      });
   });
}

function addSalary(add) {
   return new Promise(function(resolve, reject) {
      toggleLoading(true)

      return fetch('http://monkey_system_v2/addSalary', { 
         headers: { "Content-Type": "application/json" },
         method: "POST",
         body: JSON.stringify({
            perm: add.perm,
            salary: add.salario
         })
      }).then(function(response) {
         return response.json();

      }).then(function(data) {
         Toast.fire({
            title: 'Salário criado!',
            icon: 'success'
         })

         toggleLoading(false)
         resolve(data)

      }).catch(function() {
         Toast.fire({
            title: 'Erro ao criar!',
            icon: 'error'
         })

         toggleLoading(false)
         reject()
      });
   });
}

function addAdvUser(add) {
   return new Promise(function(resolve, reject) {
      toggleLoading(true)

      return fetch('http://monkey_system_v2/addAdvUser', { 
         headers: { "Content-Type": "application/json" },
         method: "POST",
         body: JSON.stringify({
            idUser : add.id,
            photo : global.screenshot,
            description : add.descricao,
            expiration : add.expiracao
         })
      }).then(function(response) {
         return response.json();

      }).then(function() {
         Toast.fire({
            title: 'Advertência adicionada!',
            icon: 'success'
         })

         global.screenshot = null;
         toggleLoading(false)
         resolve()

      }).catch(function() {
         Toast.fire({
            title: 'Erro ao adicionar!',
            icon: 'error'
         })

         global.screenshot = null;
         toggleLoading(false)
         reject()
      });
   });
}

function getProperties() {
   return new Promise(function(resolve, reject) {
      toggleLoading(true)

      return fetch('http://monkey_system_v2/getProperties', { method: 'POST' })
      .then(function(response) {
         return response.json();
      })
      .then(function(data) {
         toggleLoading(false)
         resolve(data)

      }).catch(function() {
         toggleLoading(false)
         reject()
      });
   });
}

function getSalary() {
   return new Promise(function(resolve, reject) {
      toggleLoading(true)

      return fetch('http://monkey_system_v2/getSalary', { method: 'POST' })
      .then(function(response) {
         return response.json();
      })
      .then(function(data) {
         toggleLoading(false)
         resolve(data)

      }).catch(function() {
         toggleLoading(false)
         reject()
      });
   });
}

function removeSalary(id) {
   return new Promise(function(resolve, reject) {
      Swal.fire({
         icon: 'warning',
         title: 'Remover salário',
         showCancelButton: true,
         cancelButtonText: 'Cancelar',
         confirmButtonText: 'Confirmar'

      }).then((result) => {
         if (result.isConfirmed) {
            toggleLoading(true)

            fetch('http://monkey_system_v2/removeSalary', { 
               headers: { "Content-Type": "application/json" },
               method: "POST",
               body: JSON.stringify({
                  id: id
               })
            }).then(function() {
               Toast.fire({
                  title: 'Salário removido!',
                  icon: 'success'
               })

               toggleLoading(false)
               resolve()

            }).catch(function() {
               Toast.fire({
                  title: 'Erro ao remover!',
                  icon: 'error'
               })
               
               toggleLoading(false)
               reject()
            });
         }
      });
   });
}

function purchaseProduct(id, coins) {
   return new Promise(function(resolve, reject) {
      Swal.fire({
         icon: 'warning',
         title: 'Comprar produto?',
         showCancelButton: true,
         cancelButtonText: 'Cancelar',
         confirmButtonText: 'Confirmar'

      }).then((result) => {
         if (result.isConfirmed) {
            toggleLoading(true)

            fetch('http://monkey_system_v2/purchaseProduct', { 
               headers: { "Content-Type": "application/json" },
               method: "POST",
               body: JSON.stringify({
                  id: id,
                  coins: coins
               })
            }).then(function(response) {
               return response.json();
               
            }).then(function(data) {
               if (String(data).includes('erro')) {
                  Toast.fire({
                     title: 'Erro ao comprar!',
                     icon: 'error'
                  })
               } else {
                  Toast.fire({
                     title: 'Produto comprado!',
                     icon: 'success'
                  })
               }

               toggleLoading(false)
               resolve()

            }).catch(function() {
               Toast.fire({
                  title: 'Erro ao comprar!',
                  icon: 'error'
               })
               
               toggleLoading(false)
               reject()
            });
         }
      });
   });
}

function getPurchases() {
   return new Promise(function(resolve, reject) {
      toggleLoading(true)

      return fetch(global.ecommerce.url +'user/getPurchasesPlayer?idPlayer='+ global.userLogado.id, { method: 'GET' })
      .then(function(response) {
         return response.json();
      })
      .then(function(data) {
         toggleLoading(false)
         resolve(data)

      }).catch(function() {
         toggleLoading(false)
         reject()
      });
   });
}

function purchaseProperty(id, apto, type) {
   return new Promise(function(resolve, reject) {
      toggleLoading(true)

      fetch('http://monkey_system_v2/purchaseProperty', { 
         headers: { "Content-Type": "application/json" },
         method: "POST",
         body: JSON.stringify({
            id: id,
            apto: apto,
            type: type
         })
      }).then(function(response) {
         return response.json();

      }).then(function(data) {
         if (String(data) == 'erro') {
            toggleLoading(false)
            reject()

            Toast.fire({
               title: 'Erro ao comprar!',
               icon: 'error'
            })
         } else {
            toggleLoading(false)
            resolve()

            Toast.fire({
               title: 'Imóvel comprado!',
               icon: 'success'
            })
         }
      }).catch(function() {
         Toast.fire({
            icon: 'error',
            title: 'Erro ao comprar!'
         });
         
         toggleLoading(false)
         reject()
      });
   })
}

function imageError(e) { // Imagem quadrada
   e.target.src = global.errorImg
}

function imageErrorScreen(e) { // Imagem retangular
   e.target.src = global.errorImgScreen
}