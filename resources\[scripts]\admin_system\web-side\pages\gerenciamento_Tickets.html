
<div id="local">
   <div class="cards-2">
      <div class="card mb-0" v-for="(i, index) in tickets" :key="index">
         <div class="body text-center cursor-pointer" @click="configTicket(i)">
            <h5 class="mb-2" :class="i.status == 'Aberto' ? 'text-danger' : i.status == 'Pendente avaliação' ? 'text-warning' : 'text-success'">{{ i.status }}</h5>
            <p class="text-muted m-b-0" v-if="i.status == 'Fechado'">
               <i class="fas fa-star-half-alt normal text-primary font-13 me-2"></i>Nota {{ i.rating }}
            </p>
            <p class="text-muted m-b-0">
               <i class="far fa-calendar normal text-primary font-13 me-2"></i><small>Data:</small> {{ new Date(parseInt(i.date)).toLocaleDateString('pt-BR') }}
            </p>
            <p class="text-muted m-b-0" v-if="i.status != 'Fechado'">
               <i class="far fa-arrow-alt-right normal text-primary font-13 me-2"></i><small>Descrição:</small> {{ i.title }} 
            </p>
         </div>
      </div>
   </div>
   <div v-if="tickets == null || tickets.length == 0">Nenhum ticket encontrado.</div>

   <!-- Modal mensagens -->
   <div class="modal" id="modalMsgsTicket" role="dialog">
      <div class="modal-dialog modal-lg" role="document">
         <div class="modal-content">
            <div class="modal-header">
               <h4 class="title" id="modalMsgsTicketLabel">Mensagens (Identidade {{ ticket.user_id }})</h4>
            </div>
            <div class="modal-body py-3">
               <div id="mensagens">
                  <div class="row">
                     <div class="col-12" v-for="(msg, index) in ticket.messages" :key="index">
                        <div class="card" :class="msg.user_id == global.userLogado.id ? 'minhaMensagem' : ''">
                           <div class="row font-12">
                              <div class="col-12 nome">{{ msg.name }}</div>
                              <div class="col-12">{{ msg.message }}</div>
                              <div class="col-12 data">{{ msg.date }}</div>
                           </div>
                        </div>
                     </div>
                  </div>
               </div>

               <div id="mensagensChat" class="row">
                  <div class="col">
                     <textarea class="form-control" placeholder="Mensagem..." v-model="ticketChat" @keyup.enter="configMessage(ticket.id)" maxlength="100"></textarea>
                  </div>
                  <div class="w-max-content align-self-center">
                     <button type="button" class="btn btn-primary mx-2" @click="configMessage(ticket.id)">
                        <svg viewBox="0 0 24 24" width="20" height="20">
                           <path fill="currentColor" d="M1.101 21.757 23.8 12.028 1.101 2.3l.011 7.912 13.623 1.816-13.623 1.817-.011 7.912z"></path>
                        </svg>
                     </button>
                  </div>
               </div>
            </div>
            <div class="modal-footer">
               <button type="button" class="btn btn-success waves-effect me-2" @click="configStatus(ticket.id)">
                  <i class="far fa-check me-2"></i>Finalizar
               </button>
               <button type="button" class="btn btn-default waves-effect me-2" @click="tptoUser(ticket.user_id)">
                  <i class="far fa-portal-enter me-2"></i>Teleportar
               </button>
               <button type="button" class="btn btn-danger btn-simple waves-effect" data-dismiss="modal">Fechar</button>
            </div>
         </div>
      </div>
   </div>
</div>

<script>
   var local = new Vue({
      el: '#local',
      data: {
         ticketChat: '',
         ticket: {},
         tickets: []
      }
   })

   getTickets().then((data) => {
      local.tickets = data
   })

   function configMessage(id) {
      sendMessage(id, local.ticketChat).then((data) => {
         let agora = new Date()

         local.ticket.messages.push({
            "user_id": global.userLogado.id,
            "name": global.userLogado.nome,
            "message": local.ticketChat,
            "date": agora.getHours() +":"+ agora.getMinutes() +" "+ agora.getDate() +"/"+ (agora.getMonth() + 1) +"/"+ agora.getFullYear()
         })

         setTimeout(() => {
            local.ticketChat = '';

            $('#mensagens').animate({scrollTop:$('#mensagens .row').height()}, 0)
            $("#mensagensChat textarea").focus()
         }, 150);
      })
   }

   function configStatus(id) {
      changeStatus(id, 'Pendente avaliação').then((data) => {
         $('#modalMsgsTicket').modal('hide')

         local.tickets.forEach(t => {
            if (t.id == id) {
               t.status = 'Pendente avaliação';
            }
         });
      })
   }

   function configTicket(ticket) {
      local.ticket = {};

      if (ticket.status == 'Aberto') {
         getTicket(ticket.id).then((data) => {
            local.ticket = data
            
            $('#modalMsgsTicket').modal('show')
            $("#modalMsgsTicket" ).on('shown.bs.modal', function () {
               $('#mensagens').animate({scrollTop:$('#mensagens .row').height()}, 0)
               $("#mensagensChat textarea").focus()
            })
         })
      } else if (ticket.status == 'Pendente avaliação') {
         rateTicket(ticket.id).then((data) => {
            ticket.status = 'Fechado';
         })
      }
   }
</script>