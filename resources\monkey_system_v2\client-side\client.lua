-----------------------------------------------------------------------------------------------------------------------------------------
-- VRP
-----------------------------------------------------------------------------------------------------------------------------------------
local Tunnel = module("vrp","lib/Tunnel")
local Proxy = module("vrp","lib/Proxy")
local Config = module(GetCurrentResourceName(), 'config')
vRP = Proxy.getInterface("vRP")
-----------------------------------------------------------------------------------------------------------------------------------------
-- CONNECTION
-----------------------------------------------------------------------------------------------------------------------------------------
cnVRP = {}
Tunnel.bindInterface(GetCurrentResourceName(),cnVRP)
vSERVER = Tunnel.getInterface(GetCurrentResourceName())

-----------------------------------------------------------------------------------------------------------------------------------------
-- NUI CALLBACKS
-----------------------------------------------------------------------------------------------------------------------------------------
RegisterNUICallback('getUserTickets', function(data, cb)
    local rows = vSERVER.getUserTickets()
    cb(rows)
end)

RegisterNUICallback('getUserAdvs', function(data, cb)
    local rows = vSERVER.getUserAdvs()
    cb(rows)
end)

RegisterNUICallback('getUser', function(data, cb)
    local rows = vSERVER.getUser()
    cb(rows)
end)

RegisterNUICallback('searchUsers', function(data, cb)
    local rows = vSERVER.searchUsers(data.type, data.value)
    cb(rows)
end)

RegisterNUICallback('getItems', function(data, cb)
    local rows = vSERVER.getItems()
    cb(rows)
end)

RegisterNUICallback('getVehicles', function(data, cb)
    local rows = vSERVER.getVehicles()
    cb(rows)
end)

RegisterNUICallback('getPermissions', function(data, cb)
    local rows = vSERVER.getPermissions()
    cb(rows)
end)

RegisterNUICallback('changeCoins', function(data, cb)
    vSERVER.changeCoins(data.id, data.amount)
    cb("sucesso")
end)

RegisterNUICallback('changeParkingSpaces', function(data, cb)
    vSERVER.changeParkingSpaces(data.id, data.amount)
    cb("sucesso")
end)

RegisterNUICallback('changeCharacters', function(data, cb)
    vSERVER.changeCharacters(data.id, data.amount)
    cb("sucesso")
end)

RegisterNUICallback('tptoUser', function(data, cb)
    local x,y,z = vSERVER.tptoUser(data.id)

    if x and y and z then
        SetEntityCoords(PlayerPedId(),x+0.0001,y+0.0001,z+0.0001,1,0,0,1)
        SendNUIMessage({method = 'close'})
        SetNuiFocus(false, false)
    end
    cb("sucesso")
end)

RegisterNUICallback('goodUser', function(data, cb)
    vSERVER.goodUser(data.id)
    cb("sucesso")
end)

RegisterNUICallback('changeMoney', function(data, cb)
    vSERVER.changeMoney(data.id, data.amount)
    cb("sucesso")
end)

RegisterNUICallback('toggleWhitelist', function(data, cb)
    vSERVER.toggleWhitelist(data.steam)
    cb("sucesso")
end)

RegisterNUICallback('toggleBan', function(data, cb)
    local rows = vSERVER.toggleBan(data.id, data.time)
    cb(rows)
end)

RegisterNUICallback('getInfoUser', function(data, cb)
    local rows = vSERVER.getInfoUser(data.id)
    cb(rows)
end)

-- RegisterNUICallback('getReportsUser', function(data, cb)
--     local rows = vSERVER.getReportsUser(data.id)
--     cb(rows)
-- end)

RegisterNUICallback('setPermissionUser', function(data, cb)
    vSERVER.setPermissionUser(data.id, data.permission, data.time)
    cb("sucesso")
end)

RegisterNUICallback('setItemUser', function(data, cb)
    vSERVER.setItemUser(data.id, data.item, data.amount)
    cb("sucesso")
end)

RegisterNUICallback('setVehicleUser', function(data, cb)
    vSERVER.setVehicleUser(data.id, data.vehicle)
    cb("sucesso")
end)

RegisterNUICallback('removeItemUser', function(data, cb)
    vSERVER.removeItemUser(data.id, data.item, data.amount)
    cb("sucesso")
end)

RegisterNUICallback('removeVehicleUser', function(data, cb)
    vSERVER.removeVehicleUser(data.id, data.vehicle)
    cb("sucesso")
end)

RegisterNUICallback('removePermissionUser', function(data, cb)
    vSERVER.removePermissionUser(data.id, data.permission)
    cb("sucesso")
end)

RegisterNUICallback('getVips', function(data, cb)
    local rows = vSERVER.getVips()
    cb(rows)
end)

RegisterNUICallback('searchVips', function(data, cb)
    local rows = vSERVER.searchVips(data.vip)
    cb(rows)
end)

RegisterNUICallback('getTickets', function(data, cb)
    local rows = vSERVER.getTickets()
    cb(rows)
end)

RegisterNUICallback('getTicket', function(data, cb)
    local rows = vSERVER.getTicket(data.id)
    cb(rows)
end)

RegisterNUICallback('createTicket', function(data, cb)	
    local rows = vSERVER.createTicket(data.title)

    print(#rows)
    cb(rows)
end)

RegisterNUICallback('sendMessage', function(data, cb)
    vSERVER.sendMessage(data.id, data.message)
    cb("sucesso")
end)

-- RegisterNUICallback('tpTicket', function(data, cb)
--     local x,y,z = vSERVER.tpTicket(data.idTicket)

--     if x and y and z then
--         SetEntityCoords(PlayerPedId(),x+0.0001,y+0.0001,z+0.0001,1,0,0,1)
--     end
--     cb("sucesso")
-- end)

RegisterNUICallback('changeStatus', function(data, cb)	
    vSERVER.changeStatus(data.id, data.status)
    cb("sucesso")
end)

RegisterNUICallback('rateTicket', function(data, cb)	
    vSERVER.rateTicket(data.id, data.rating)
    cb("sucesso")
end)

RegisterNUICallback('getSalary', function(data, cb)
    local rows = vSERVER.getSalary()
    cb(rows)
end)

RegisterNUICallback('addSalary', function(data, cb)
    local rows = vSERVER.addSalary(data.perm, data.salary)
    cb(rows)
end)

RegisterNUICallback('removeSalary', function(data, cb)
    vSERVER.remSalary(data.id)
    cb("sucesso")
end)

RegisterNUICallback('searchAdvs', function(data, cb)
    local rows = vSERVER.searchAdvs(data.type, data.value)
    cb(rows)
end)

RegisterNUICallback('addAdvUser', function(data, cb)
    local rows = vSERVER.addAdvUser(data.idUser, data.photo, data.expiration, data.description)
    cb(rows)
end)

RegisterNUICallback('sendAd', function(data, cb)
    vSERVER.announce(data.type, data.value, data.message)
    cb("sucesso")
end)

RegisterNUICallback('getOnline', function(data, cb)
    local rows = vSERVER.getOnline()
    cb(rows)
end)

RegisterNUICallback('purchaseProduct', function(data, cb)
    local retorno = vSERVER.purchaseProduct(data.id, data.coins)
    cb(retorno)
end)









RegisterNUICallback('getInfo', function(data, cb)
    local rows = vSERVER.getInfo()
    cb(rows)
end)

RegisterNUICallback('getPlayerTickets', function(data, cb)
    local rows = vSERVER.getPlayerTickets()
    cb(rows)
end)

RegisterNUICallback('getPlayers', function(data, cb)
    local rows = vSERVER.getPlayers(data.type, data.id, data.steam, data.perm, data.whitelist, data.page, data.code, data.online)
    cb(rows)
end)

RegisterNUICallback('getAdvUser', function(data, cb)
    local rows = vSERVER.getAdvUser(data.idUser)
    cb(rows)
end)

RegisterNUICallback('takePhoto', function(data, cb)
    SendNUIMessage({method = 'close'})
	SetNuiFocus(false, false)
    CreateMobilePhone(1)
	CellCamActivate(true, true)
	phone = true
end)

-- RegisterNUICallback('getItens', function(data, cb)
--     local rows = vSERVER.getItens(data.idUser)
--     cb(rows)
-- end)

RegisterNUICallback('setBlacklist', function(data, cb)
    vSERVER.setBlacklist(data.steam)
    cb("sucesso")
end)

-- RegisterNUICallback('getPermissions', function(data, cb)
--     local rows = vSERVER.getPermissions(data.idUser)
--     cb(rows)
-- end)


RegisterNUICallback('getOrganizations', function(data, cb)
    local rows = vSERVER.getOrganizations()
    cb(rows)
end)

RegisterNUICallback('getMyOrganizations', function(data, cb)
    local rows = vSERVER.getMyOrganizations()
    cb(rows)
end)

RegisterNUICallback('getOrganizationById', function(data, cb)
    local rows = vSERVER.getOrganizationById(data.id)
    cb(rows)
end)

RegisterNUICallback('addMemberOrganization', function(data, cb)
    if vSERVER.addMemberOrganization(data.idOrganization, data.permission, data.idPlayer) then
        cb({msg = "sucesso"})
    else
        cb({msg = "erro"})
    end
end)

RegisterNUICallback('removeMemberOrganization', function(data, cb)
    vSERVER.removeMemberOrganization(data.idOrganization, data.idPlayer)
    cb("sucesso")
end)

RegisterNUICallback('addRole', function(data, cb)
    vSERVER.addRole(data.idOrganization, data.permission)
    cb("sucesso")
end)

RegisterNUICallback('removeRole', function(data, cb)
    vSERVER.removeRole(data.idOrganization, data.permission)
    cb("sucesso")
end)

RegisterNUICallback('renameRole', function(data, cb)
    vSERVER.renameRole(data.idOrganization, data.permission, data.name)
    cb("sucesso")
end)

RegisterNUICallback('addOrganization', function(data, cb)
    vSERVER.addOrganization(data.urlLogo, data.name, data.maxMembers, data.idOwner)
    cb("sucesso")
end)

RegisterNUICallback('editOrganization', function(data, cb)
    vSERVER.editOrganization(data.idOrganization, data.urlLogo, data.name, data.maxMembers, data.idOwner)
    cb("sucesso")
end)

RegisterNUICallback('deleteOrganization', function(data, cb)
    vSERVER.deleteOrganization(data.idOrganization)
    cb("sucesso")
end)

RegisterNUICallback('getLocation', function(data, cb)
    local c = {}
    c.x,c.y,c.z = table.unpack(GetEntityCoords(PlayerPedId(),true))
    cb(c.x..", "..c.y..", "..c.z)
end)

RegisterNUICallback('tp', function(data, cb)
    local coord = split(data.coord, ",")
    local x,y,z = coord[1],coord[2],coord[3]
    
    SetEntityCoords(PlayerPedId(),x+0.0001,y+0.0001,z+0.0001,1,0,0,1)
    cb("sucesso")
end)

RegisterNUICallback('getLoja', function(data, cb)
    local rows = vSERVER.getLoja()
    cb(rows)
end)

RegisterNUICallback('buyItem', function(data, cb)
    local rows = vSERVER.buyItem(data.item, data.category)
    cb(rows)
end)

RegisterNUICallback('getPurchasesHistory', function(data, cb)
    local rows = vSERVER.getPurchasesHistory(data.dateStart, data.dateEnd, data.idUser)
    cb(rows)
end)

RegisterNUICallback('close', function(data, cb)
	SetNuiFocus(false, false)
    cb("sucesso")
end)

RegisterNUICallback('getPlayersOnline', function(data, cb)
    local rows = vSERVER.getPlayersOnline(data.type)
    cb(rows)
end)

RegisterNUICallback('getProperties', function(data, cb)
    local rows = vSERVER.getProperties()
    cb(rows)
end)

RegisterNUICallback('purchaseProperty', function(data, cb)
    local rows = vSERVER.purchaseProperty(data.id, data.apto, data.type)
    cb(rows)
end)








-----------------------------------------------------------------------------------------------------------------------------------------
-- FUNCTIONS
-----------------------------------------------------------------------------------------------------------------------------------------
function cnVRP.getPositionPlayer()
    return table.unpack(GetEntityCoords(PlayerPedId(),true))
end

function Dump(o)
    if type(o) == 'table' then
        local s = '{ '
        for k, v in pairs(o) do
            if type(k) ~= 'number' then k = '"' .. k .. '"' end
            s = s .. '[' .. k .. '] = ' .. Dump(v) .. ','
        end
        return s .. '} '
    else
        return tostring(o)
    end
end

function split(s, delimiter)
    result = {};
    for match in (s..delimiter):gmatch("(.-)"..delimiter) do
        table.insert(result, match);
    end
    return result;
end

-----------------------------------------------------------------------------------------------------------------------------------------
-- REGISTER COMMAND
-----------------------------------------------------------------------------------------------------------------------------------------
RegisterCommand(Config.commandTablet, function()
        SetNuiFocus(true, true)
        SendNUIMessage({method = 'open'})
end)

RegisterCommand("pegarcoord", function()
    local c = {}
    c.x,c.y,c.z = table.unpack(GetEntityCoords(PlayerPedId(),true))

    SendNUIMessage({ method = 'sendCoord', x = c.x, y = c.y, z = c.z})
end)

-----------------------------------------------------------------------------------------------------------------------------------------
-- THREADS
-----------------------------------------------------------------------------------------------------------------------------------------
Citizen.CreateThread(function()
    while true do
        if IsControlJustPressed(0, Config.keyTablet) then
                SetNuiFocus(true, true)
                SendNUIMessage({method = 'open'})
        end
        Citizen.Wait(10)
    end
end)


Citizen.CreateThread(function()
	while true do
		Citizen.Wait(Config.salaryMinutes*60000)
        TriggerServerEvent("monkey_system_v2:salary")
	end
end)

-----------------------------------------------------------------------------------------------------------------------------------------
---- CAMERA
-----------------------------------------------------------------------------------------------------------------------------------------
Citizen.CreateThread(function()
	DestroyMobilePhone()
	while true do
		Citizen.Wait(0)

		if IsControlJustPressed(0, 176) and phone == true then -- TAKE.. PIC
		
			exports['screenshot-basic']:requestScreenshotUpload('https://discord.com/api/webhooks/1012806801374318693/PvGZ_tL8DCBnjMdDVHWJEGqmdKppeD0096HwmuhZ4sOPglcpZuk9EHcsLMoNKik8K6Pz', 'files[]', function(data)
				local resp = json.decode(data)
				local url = resp["attachments"][1]['url']

                print(url)

                SendNUIMessage({
                    method = "sendUrl", 
                    URL = url
                })

                SetNuiFocus(true, true)
                SendNUIMessage({method = 'open'})

				DestroyMobilePhone()
				phone = false
				CellCamActivate(false, false)
				if firstTime == true then 
					firstTime = false 
					Citizen.Wait(2500)
					displayDoneMission = true
				end

				-- Triggermethod("vrpex_tablet:openTablet")
			end)

		end
				
		if IsControlJustPressed(1, 177) and phone == true then -- CLOSE PHONE
			DestroyMobilePhone()
			phone = false
			CellCamActivate(false, false)
			if firstTime == true then 
				firstTime = false 
				Citizen.Wait(2500)
				displayDoneMission = true
			end
		end
			
		if phone == true then
			HideHudComponentThisFrame(7)
			HideHudComponentThisFrame(8)
			HideHudComponentThisFrame(9)
			HideHudComponentThisFrame(6)
			HideHudComponentThisFrame(19)
			HideHudAndRadarThisFrame()
		end
			
		ren = GetMobilePhoneRenderId()
		SetTextRenderId(ren)
		
		-- Everything rendered inside here will appear on your phone.
		
		SetTextRenderId(1) -- NOTE: 1 is default
	end
end)