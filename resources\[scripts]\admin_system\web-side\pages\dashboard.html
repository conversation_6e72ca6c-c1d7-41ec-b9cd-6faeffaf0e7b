<div id="local">
   <div class="card">
      <div class="body">
         <h4 class="mb-2 mt-0">Seja bem vindo!</h4>
         <p class="mb-1">Lorem ipsum dolor sit amet, consectetur adipiscing elit. Nam rhoncus semper quam, in sodales enim vulputate ac. In hac habitasse platea dictumst. Curabitur eu ante vel enim sagittis fermentum. Suspendisse dictum vestibulum ipsum, a porta felis morbi.</p>
      </div>
   </div>

   <div class="card" v-if="jogadorLogado != null && jogadorLogado.id != null">
      <div class="body widget-user">
         <div class="text-right" style="float: right;">
            <h2 class="m-b-0 text-center">{{ jogadorLogado.id }}</h2>
            <small class="info">Identidade</small>
         </div>
         <img src="images/man.png" alt="Avatar">
         <div class="wid-u-info col-7">
            <h5 class="mb-2">{{ jogadorLogado.nome }}</h5>
            <p class="text-muted m-b-0"> 
               <i class="far fa-phone-alt text-primary me-1"></i> {{ jogadorLogado.telefone }} 
               <span class="ms-5"><i class="far fa-address-card text-primary me-1"></i> {{ jogadorLogado.rg }}</span>
            </p>
         </div>
      </div>
   </div>

   <div class="card" v-if="jogadorLogado != null && jogadorLogado.id != null">
      <div class="row m-0 profile_state">
         <div class="col-6 ps-0">
            <div class="body p-3">
               <h4 class="mt-0 mb-1">{{ jogadorLogado.coins }}</h4>
               <p class="mt-1 mb-0"><i class="far fa-coin text-warning me-1"></i> Coins</p>
            </div>
         </div>
         <div class="col-6">
            <div class="body p-3">
               <h4 class="mt-0 mb-1">{{ jogadorLogado.banco }}</h4>
               <p class="mt-1 mb-0"><i class="far fa-money-bill-wave-alt text-success me-1"></i> Banco</p>
            </div>
         </div>
         <!-- <div class="col-4 pe-0">
            <div class="body p-3">
               <h4 class="mt-0 mb-1">{{ jogadorLogado.garagens }}</h4>
               <p class="mt-1 mb-0"><i class="far fa-garage text-danger me-1"></i> Garagens</p>
            </div>
         </div> -->
      </div>
   </div>
</div>

<script>
   var local = new Vue({
      el: '#local',
      data: {
         jogadorLogado: {}
      }
   })

   getInfo().then((data) => {
      local.jogadorLogado = data
   })
</script>