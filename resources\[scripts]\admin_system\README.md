# Admin System - Convertido para VRP

Sistema de administração completo convertido do monkey_system_v2 para ser 100% compatível com o framework VRP do projeto.

## 📋 Funcionalidades

### 🎫 Sistema de Tickets
- Criação e gerenciamento de tickets
- Sistema de mensagens em tempo real
- Avaliação de atendimento
- Status de tickets (Aberto/Fechado)

### 👥 Gerenciamento de Jogadores
- Busca avançada de jogadores
- Edição de dinheiro, gemas e itens
- Gerenciamento de permissões
- Sistema de ban/unban
- Teleporte para jogadores
- Revivar jogadores

### 🛒 Sistema de Loja VIP
- Compra de VIPs com gemas
- Histórico de compras
- Sistema de entrega automática

### 📢 Sistema de Anúncios
- Criação de anúncios
- Sistema de expiração
- Gerenciamento de anúncios ativos

### 💰 Sistema de Salários
- Configuração de salários por permissão
- Pagamento automático
- Gerenciamento de salários

### 🏢 Sistema de Organizações
- Criação e gerenciamento de organizações
- Sistema de permissões internas
- Blacklist de organizações

## 🚀 Instalação

### 1. Banco de Dados
Execute o arquivo `database.sql` no seu banco de dados MySQL:
```sql
source resources/[scripts]/admin_system/database.sql
```

### 2. Configuração
Edite o arquivo `config.lua` conforme suas necessidades:
- Altere as credenciais de login
- Configure as webhooks do Discord
- Ajuste as permissões
- Configure os VIPs e preços

### 3. Dependências
Certifique-se de que o VRP está funcionando corretamente e que as seguintes configurações estão presentes:
- `@vrp/config/Groups.lua` - Configuração de grupos
- `@vrp/config/Item.lua` - Lista de itens
- `@vrp/config/Vehicle.lua` - Lista de veículos

### 4. Ativação
Adicione o resource no seu `server.cfg`:
```
ensure admin_system
```

## ⚙️ Configuração

### Permissões Principais
- `Admin` - Acesso completo ao sistema
- `Police` - Acesso limitado para polícia
- `Paramedic` - Acesso limitado para paramédicos

### Comandos
- `/admin` - Abre o painel administrativo
- `F4` - Tecla de atalho para abrir o painel

### Webhooks
Configure as webhooks no `config.lua` para receber notificações no Discord:
- `webhook_veiculos` - Entrega de veículos
- `webhook_money` - Transações de dinheiro
- `webhook_coins` - Transações de gemas
- `webhook_item` - Entrega de itens
- `webhook_permissao` - Alterações de permissões

## 🔧 Adaptações Realizadas

### Framework VRP
- Convertido de `vRP.getUserId()` para `vRP.Passport()`
- Adaptado `vRP.userIdentity()` para `vRP.Identity()`
- Convertido sistema de inventário para `vRP.Inventory()`
- Adaptado sistema de banco para `vRP.GetBank()` e `vRP.GiveBank()`
- Convertido sistema de permissões para `vRP.HasPermission()`

### Banco de Dados
- Renomeadas todas as tabelas de `monkey_system_*` para `admin_system_*`
- Adaptadas queries para o padrão VRP
- Criados prepares específicos para o VRP

### Interface
- Mantida a interface NUI original
- Adaptados os callbacks para o novo backend
- Corrigidas as funções de comunicação client-server

## 🐛 Solução de Problemas

### Erro: "attempt to call a nil value"
Verifique se todas as dependências do VRP estão carregadas corretamente.

### Jogadores não aparecem na busca
Certifique-se de que a tabela `characters` existe e está populada.

### Sistema de salário não funciona
Verifique se a tabela `admin_system_salary` foi criada e populada com os dados iniciais.

### Interface não abre
Verifique se todos os arquivos da pasta `web-side` foram copiados corretamente.

## 📝 Changelog

### v1.0.0
- Conversão completa do monkey_system_v2 para VRP
- Adaptação de todas as funções para o framework VRP
- Criação de sistema de prepares específico
- Manutenção da interface NUI original
- Sistema 100% funcional

## 🤝 Suporte

Para suporte técnico ou dúvidas sobre o sistema, entre em contato com a equipe de desenvolvimento.

## 📄 Licença

Este sistema foi convertido e adaptado para uso exclusivo no projeto Creative Network.
