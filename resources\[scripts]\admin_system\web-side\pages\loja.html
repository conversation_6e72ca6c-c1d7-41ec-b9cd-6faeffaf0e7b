<div id="local">
   <div class="cards-1">
      <div class="product__item product__electronic-item" v-for="(p, index) in resultado" :key="index">
         <div class="product__thumb position-relative">
            <a href="javascript:;" class="w-img">
               <img :src="p.fotos.length == 0 ? '' : p.fotos[0].foto" alt="product" @error="imageError" @click="viewPhoto">
            </a>
         </div>
         <div class="product__content">
            <h6 class="product-name">{{ p.nome }}</h6>
            <span class="price" v-if="p.estoque >= 1">
               <i class="far fa-coins text-warning font-12 me-1"></i> {{ parseInt(parseFloat(p.preco) * global.ecommerce.multiplyCoin) }}
            </span>
            <span class="price font-14" v-else>
               <i class="far fa-box text-danger font-12 me-1"></i> Sem estoque
            </span>
         </div>
         <div class="product__add-btn" v-if="p.estoque >= 1">
            <button type="button" @click="purchaseProduct(p.id, parseInt(parseFloat(p.preco) * global.ecommerce.multiplyCoin))"><i class="far fa-plus me-1"></i> Comprar</button>
         </div>
      </div>
   </div>
   <div v-if="resultado == null || resultado.length == 0">Nenhum produto encontrado.</div>
</div>

<script>
   var local = new Vue({
      el: '#local',
      data: {
         resultado: []
      }
   })

   setTimeout(() => {
      getProducts().then((data) => {
         local.resultado = data
      })
   }, 150);
</script>