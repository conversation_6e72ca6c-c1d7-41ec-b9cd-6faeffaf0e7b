<div id="local">
   <div class="cards-1">
      <div class="product__item product__electronic-item" v-for="(p, index) in resultado" :key="index">
         <div class="product__thumb position-relative">
            <a href="javascript:;" class="w-img">
               <img :src="p.homePhoto == null ? '' : p.homePhoto" alt="product" @error="imageError" @click="viewPhoto">
            </a>
         </div>
         <div class="product__content">
            <h6 class="product-name delimitarTexto">
               Imóvel nº {{ p.homeId }}
               <small class="ms-1 font-11 text-secondary">{{ p.homeApto > 0 ? '(Apto nº '+ p.homeApto +')' : '' }}</small>
            </h6>
            <div class="rating mb-2">
               <i class="far fa-treasure-chest font-11 text-primary me-1"></i> {{ p.homeChestWeight }} <small class="font-11 text-secondary">Kg</small><br>
               <i class="far fa-key font-11 text-primary me-1"></i> {{ p.homeKeyAmount }} <small class="font-11 text-secondary">Chaves</small>
            </div>
            <span class="price font-14 delimitarTexto">
               <small class="font-11 text-secondary">$</small> {{ p.homeValue }}
               <small class="mx-1 text-secondary"> | </small> {{ p.homeValueCoins }} <small class="font-11 text-secondary">Coins</small>
            </span>
         </div>
         <div class="product__add-btn">
            <button type="button" @click="configPurchaseProperty(p.homeId, p.homeApto, index)"><i class="far fa-plus me-1"></i> Comprar</button>
         </div>
      </div>
   </div>
   <div v-if="resultado == null || resultado.length == 0">Nenhum produto encontrado.</div>
</div>

<script>
   var local = new Vue({
      el: '#local',
      data: {
         resultado: []
      }
   })

   setTimeout(() => {
      getProperties().then((data) => {
         local.resultado = data
      })
   }, 150);

   function configPurchaseProperty(id, apto, index) {
      Swal.fire({
         icon: 'warning',
         title: 'Forma de pagamento',
         confirmButtonText: 'Dinheiro',
         showCancelButton: true,
         cancelButtonText: 'Cancelar',
         showDenyButton: true,
         denyButtonText: 'Coins'

      }).then((result) => {
         if (result.isDenied) {
            purchaseProperty(id, apto, 'Coins').then(function() {
               local.resultado.splice(index, 1)
            })
         } else if (result.isConfirmed) {
            purchaseProperty(id, apto, 'Dinheiro').then(function() {
               local.resultado.splice(index, 1)
            })
         }
      });
   }
</script>