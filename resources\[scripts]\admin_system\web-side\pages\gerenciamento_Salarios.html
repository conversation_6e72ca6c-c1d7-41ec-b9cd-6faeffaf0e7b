
<div id="local">
   <div class="cards-2">
      <div class="card mb-0" v-for="(s, index) in salarios" :key="index">
         <div class="body text-center removeToHover" @click="configRemSalary(s.id, index)">
            <h5 class="mb-2">{{ s.permission }}</h5>
            <h5 class="mb-2"><i class="far fa-trash normal"></i><br>Clique para remover</h5>
            <p class="text-muted m-b-0"> 
               <i class="far fa-arrow-alt-right normal text-primary me-1"></i><small>Salário:</small> {{ s.salary }} 
            </p>
         </div>
      </div>
   </div>
   <div v-if="salarios == null || salarios.length == 0">Nenhum salário encontrado.</div>

   <!-- Modal salario -->
   <div class="modal fade" id="modalAddSalary" role="dialog">
      <div class="modal-dialog modal-md" role="document">
         <div class="modal-content">
            <div class="modal-header">
               <h4 class="title" id="modalAddSalaryLabel">Adicionar salário</h4>
            </div>
            <div class="modal-body py-3">
               <div class="card">
                  <div class="body">
                     <div class="row">
                        <div class="col-12 mb-2">
                           <label class="form-label"><i class="far fa-lock text-primary me-1"></i> Permissão</label>
                           <input type="text" class="form-control" v-model="add.perm" />
                           <!-- <select class="form-control shadow-none" v-model="add.perm">
                              <option v-for="(permissao, index) in global.permissoes" :key="index" :value="permissao">{{ permissao }}</option>
                           </select> -->
                        </div>
                        <div class="col-12">
                           <label class="form-label"><i class="far fa-money-bill-wave-alt text-primary me-1"></i> Salário</label>
                           <input type="text" class="form-control" v-model="add.salario" />
                        </div>
                     </div>
                  </div>
               </div>
            </div>
            <div class="modal-footer">
               <button type="button" class="btn btn-success waves-effect me-2" @click="configAddSalary">
                  <i class="far fa-check me-2"></i>Salvar
               </button>
               <button type="button" class="btn btn-danger btn-simple waves-effect" data-dismiss="modal">Fechar</button>
            </div>
         </div>
      </div>
   </div>

   <a href="javascript:;" class="btn-float btn-primary" @click="showAddSalary">
      <i class="fas fa-plus normal"></i>
   </a>
</div>

<script>
   var local = new Vue({
      el: '#local',
      data: {
         add: {'perm': null, 'salario': 0},
         salarios: []
      }
   })

   getPermissions().then((data) => {
      if (global.permissoes[0] != null) {
         local.add.perm = global.permissoes[0]

         setTimeout(() => {
            $('select').selectpicker();
         }, 300);
      }
   })

   getSalary().then((data) => {
      local.salarios = data
   })

   function showAddSalary() {
      $('#modalAddSalary').modal('show')
   }

   function configAddSalary() {
      addSalary(local.add).then((data) => {
         local.salarios = data
         local.add = {'perm': null, 'salario': 0}

         $('#modalAddSalary').modal('hide')
      })
   }

   function configRemSalary(id, index) {
      removeSalary(id).then((data) => {
         local.salarios.splice(index, 1)
      })
   }
</script>