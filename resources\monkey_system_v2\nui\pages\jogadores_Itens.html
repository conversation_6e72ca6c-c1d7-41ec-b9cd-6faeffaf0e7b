<div id="local" class="card">
   <div class="body p-3 pt-2 mx-1 mb-1">
      <label class="form-label"><i class="far fa-backpack text-primary me-1"></i> Item</label>
      <input type="text" class="form-control searchInput" v-model="add.searchItem" placeholder="Pesquisar..." />
   </div>

   <div id="searchItem" class="cards">
      <div class="body text-center p-2 cursor-pointer delimitarTexto weight-700 font-14" v-for="(item, index) in global.itens" :key="index" @click="configAddItem(item)">
         <img class="lazyload mb-2" :src="global.urlFotoItens + item +'.png'" @error="imageError" /><br>
         {{ item }}
      </div>
   </div>

   <div v-if="global.itens == null || global.itens.length == 0">Nenhum item encontrado.</div>
</div>

<script>
   var local = new Vue({
      el: '#local',
      data: {
         add: {'searchItem': ''}
      },
      watch: {
         'add.searchItem' : function (val) {
            var value = val.toLowerCase()

            $("#searchItem .body").filter(function () {
               $(this).toggle($(this).text().toLowerCase().indexOf(value) > -1)
            })
         }
      }
   })

   getItems().then((data) => {
      lazyload()

      setTimeout(() => {
         $(".searchInput").focus()
      }, 300);
   })

   function configAddItem(item) {
      Swal.fire({
         icon: 'warning',
         title: 'Adicionar item',
         text: 'Informe a identidade',
         inputPlaceholder: 'Identidade',
         input: 'text',
         showCancelButton: true,
         cancelButtonText: 'Cancelar',
         confirmButtonText: 'Confirmar',
         inputValidator: (value) => {
            if (!$.isNumeric(value)) {
               return 'Identidade inválida!'
            }
         }
      }).then((result) => {
         if (result.isConfirmed) {
            setItemUser(parseInt(result.value), item)
         }
      })
   }
   
</script>