-----------------------------------------------------------------------------------------------------------------------------------------
-- ADMIN SYSTEM CONFIG
-----------------------------------------------------------------------------------------------------------------------------------------
Config = {}

-- Sistema de Login
Config.login = "CidadeDosSonhos"
Config.senha = "CidadeDosSonhos123"

-- Tecla e comando para abrir o sistema
Config.keyTablet = 83 -- F4
Config.commandTablet = "admin"

-- Evento de notificação (compatível com o sistema do projeto)
Config.notifyEvent = "Notify"

-- Logo do sistema
Config.logo = "https://cdn.discordapp.com/attachments/943970427804475402/1025087507978801222/logo_monkey_sem_fundo.png"

-- URLs para imagens
Config.urlImgLoja = "https://j4v4.site/MC/carrosVip/"
Config.urlImgItens = "https://j4v4.site/MC/itens/"
Config.urlImgCarros = "https://j4v4.site/MC/carros/"

-- Categoria de carros que aparecerá na loja
Config.carsStore = "donate"

-- Tempo para receber salário (em minutos)
Config.salaryMinutes = 60

-- Status de ticket fechado
Config.closedTicketStatus = "Fechado"

-- Mensagens do sistema
Config.notEnoughMoney = 'Você não tem dinheiro suficiente para esta compra!'
Config.notEnoughCoin = 'Você não tem gemas suficientes para esta compra!'
Config.salaryReceived = 'Você recebeu seu salário.'

-- Grupos VIP (adaptado para o sistema VRP)
Config.vips = {"Bronze","Prata","Ouro","Platina"}

-- Permissões (adaptadas para o sistema VRP)
Config.adminPerm = "Admin"
Config.ownerPerm = "Admin"
Config.policePerm = "Police"
Config.paramedicPerm = "Paramedic"

-- Loja VIP
Config.loja = {
    ["vips"] = {
        ["Bronze"] = {
            ["item"] = "premium01",
            ["description"] = "VIP Bronze",
            ["value"] = 30,
            ["quantity"] = 1 
        },
        ["Prata"] = {
            ["item"] = "premium02",
            ["description"] = "VIP Prata",
            ["value"] = 50,
            ["quantity"] = 1 
        },
        ["Ouro"] = {
            ["item"] = "premium03",
            ["description"] = "VIP Ouro",
            ["value"] = 100,
            ["quantity"] = 1 
        },
        ["Platina"] = {
            ["item"] = "premium04",
            ["description"] = "VIP Platina",
            ["value"] = 180,
            ["quantity"] = 1 
        },
    }
}

-- WEBHOOKS
Config.webhook_veiculos = "Sua webhook aqui"
Config.webhook_desban = "Sua webhook aqui"
Config.webhook_permissao = "Sua webhook aqui"
Config.webhook_money = "Sua webhook aqui"
Config.webhook_coins = "Sua webhook aqui"
Config.webhook_garage = "Sua webhook aqui"
Config.webhook_item = "Sua webhook aqui"

-- Configurações de propriedades
Config.diasIPTU = 15
Config.diasRemoverHome = 3
Config.taxaIPTU = 10 

-- Upgrades de propriedades
Config.upgrades = {
    ['pacote1'] = {
        ['casas'] = {
            [1] = { ['i'] = "n1t1_1", ['v'] = 200000 },
            [2] = { ['i'] = "n1t1_2", ['v'] = 250000 },
            [3] = { ['i'] = "n1t1_3", ['v'] = 300000 },
            [4] = { ['i'] = "n1t2_1", ['v'] = 200000 },
            [5] = { ['i'] = "n1t2_2", ['v'] = 250000 },
            [6] = { ['i'] = "n1t2_3", ['v'] = 300000 },
            [7] = { ['i'] = "n1t3_1", ['v'] = 200000 },
            [8] = { ['i'] = "n1t3_2", ['v'] = 250000 },
            [9] = { ['i'] = "n1t3_3", ['v'] = 300000 },
        },
        ['init_bau'] = 25,
        ['init_chaves'] = 1,
        ['multiplicador'] = 2,
        ['valor_upgrade_init_bau'] = 25000,
        ['valor_upgrade_init_chave'] = 25000
    }
}

return Config
