-----------------------------------------------------------------------------------------------------------------------------------------
-- VRP
-----------------------------------------------------------------------------------------------------------------------------------------
local Tunnel = module("vrp","lib/Tunnel")
local Proxy = module("vrp","lib/Proxy")
local Config = module(GetCurrentResourceName(), 'config')

vRP = Proxy.getInterface("vRP")
vRPclient = Tunnel.getInterface("vRP")

-- Carr<PERSON>ar configurações do VRP
Groups = Groups or {}
ItemList = ItemList or {}
VehicleList = VehicleList or {}

-----------------------------------------------------------------------------------------------------------------------------------------
-- CONNECTION
-----------------------------------------------------------------------------------------------------------------------------------------
cnVRP = {}
Tunnel.bindInterface(GetCurrentResourceName(),cnVRP)
vCLIENT = Tunnel.getInterface(GetCurrentResourceName())

-----------------------------------------------------------------------------------------------------------------------------------------
-- PREPARES
-----------------------------------------------------------------------------------------------------------------------------------------
vRP.Prepare("adminSystem/getTicketsbyIdUser","SELECT * FROM admin_system_tickets WHERE user_id = @user_id AND status <> 'Fechado' ORDER BY id DESC")
vRP.Prepare("adminSystem/getOpenTickets","SELECT * FROM admin_system_tickets WHERE status = 'Aberto' ORDER BY id DESC")
vRP.Prepare("adminSystem/getClosedTickets","SELECT * FROM admin_system_tickets WHERE status <> 'Aberto' AND `date` BETWEEN CONCAT(CURDATE() ,' 00:00:00') AND CONCAT(CURDATE() , ' 23:59:59') ORDER BY id DESC")
vRP.Prepare("adminSystem/getTicketbyId","SELECT * FROM admin_system_tickets WHERE id = @id")
vRP.Prepare("adminSystem/addTicket","INSERT INTO admin_system_tickets (user_id, `date`, status, rating, title) VALUES(@user_id, @date, 'Aberto', '', @title)")
vRP.Prepare("adminSystem/updTicketStatus","UPDATE admin_system_tickets SET status = @status WHERE id = @id")
vRP.Prepare("adminSystem/updTicketRate","UPDATE admin_system_tickets SET rating = @rating WHERE id = @id")

vRP.Prepare("adminSystem/getMessagesbyTicketId","SELECT * FROM admin_system_ticket_messages WHERE idTicket = @idTicket ORDER BY id ASC LIMIT 20")
vRP.Prepare("adminSystem/addMessage","INSERT INTO admin_system_ticket_messages (user_id, name, message, `date`, idTicket) VALUES(@user_id, @name, @message, @date, @idTicket)")

vRP.Prepare("adminSystem/deleteAdv","UPDATE admin_system_adv SET deleted = 1 WHERE id = @id")
vRP.Prepare("adminSystem/getExpiredAdvs","SELECT * FROM admin_system_adv WHERE NOW() > DATE_ADD(date, INTERVAL +validity DAY) AND deleted = 0 LIMIT 25")
vRP.Prepare("adminSystem/getActiveAdvs","SELECT * FROM admin_system_adv WHERE NOW() < DATE_ADD(date, INTERVAL +validity DAY) AND deleted = 0")
vRP.Prepare("adminSystem/getAdvsbyIdUser","SELECT * FROM admin_system_adv WHERE user_id = @user_id AND deleted = 0 ORDER BY id DESC")
vRP.Prepare("adminSystem/addAdv","INSERT INTO admin_system_adv (user_id, description, validity, `date`, photo) VALUES(@user_id, @desc, @expiration, @date, @photo)")

vRP.Prepare("adminSystem/getOrganizations","SELECT * FROM admin_system_organization")
vRP.Prepare("adminSystem/getOrganizationbyId","SELECT * FROM admin_system_organization WHERE id = @id")
vRP.Prepare("adminSystem/addOrganization","INSERT INTO admin_system_organization (dateCreation, idOwner, maxMembers, name, urlPhoto) VALUES(@date, @idOwner, @maxMembers, @name, @urlPhoto)")
vRP.Prepare("adminSystem/updOrganization","UPDATE admin_system_organization SET name = @name, urlPhoto = @urlPhoto, maxMembers = @maxMembers, idOwner = @idOwner WHERE id = @id")
vRP.Prepare("adminSystem/delOrganization","DELETE FROM admin_system_organization WHERE id = @id")

vRP.Prepare("adminSystem/getSalary","SELECT * FROM admin_system_salary")
vRP.Prepare("adminSystem/addSalary","INSERT INTO admin_system_salary (permission, salary) VALUES(@perm, @salary)")
vRP.Prepare("adminSystem/remSalary","DELETE FROM admin_system_salary WHERE id = @id")

vRP.Prepare("adminSystem/getPlayers","SELECT * FROM characters LIMIT 20 OFFSET @offset")
vRP.Prepare("adminSystem/getPlayersbyId","SELECT * FROM characters WHERE id = @id LIMIT 50 OFFSET @offset")
vRP.Prepare("adminSystem/getPlayersByName","SELECT * FROM characters WHERE name LIKE @name OR name2 LIKE @name LIMIT 50 OFFSET @offset")

vRP.Prepare("adminSystem/addPurchaseHistory","INSERT INTO admin_system_purchases_history (user_id, item, category, `date`) VALUES(@user_id, @item, @category, @date)")
vRP.Prepare("adminSystem/getPurchasesHistory","SELECT * FROM admin_system_purchases_history WHERE user_id = @user_id ORDER BY id DESC")
vRP.Prepare("adminSystem/getPurchasesHistorybyDate","SELECT * FROM admin_system_purchases_history WHERE date BETWEEN @dateStart AND @dateEnd ORDER BY id DESC")
vRP.Prepare("adminSystem/getLastPurchasesHistory","SELECT * FROM admin_system_purchases_history ORDER BY id DESC LIMIT 20")

vRP.Prepare("adminSystem/setBlacklistOrg","INSERT INTO admin_system_organization_blacklist (user_id, `date`) VALUES(@user_id, @date)")
vRP.Prepare("adminSystem/delBlacklistOrg","DELETE FROM admin_system_organization_blacklist WHERE user_id = @user_id")
vRP.Prepare("adminSystem/getBlacklistOrg","SELECT * FROM admin_system_organization_blacklist WHERE user_id = @user_id AND CURDATE() < DATE_ADD(date, INTERVAL 30 DAY)")

-----------------------------------------------------------------------------------------------------------------------------------------
-- CONFIG
-----------------------------------------------------------------------------------------------------------------------------------------
local logo = Config.logo
local vips = Config.vips
local loja = Config.loja
local notify = Config.notifyEvent

-----------------------------------------------------------------------------------------------------------------------------------------
-- FUNCTIONS
-----------------------------------------------------------------------------------------------------------------------------------------
function getAllPermission(Passport)
    local permissions = {}
    
    for Permission,_ in pairs(Groups) do
        if vRP.HasPermission(Passport,Permission) then
            local obj = {}
            obj.permiss = Permission
            table.insert(permissions, obj)
        end
    end
    
    return permissions
end

function cnVRP.getUserTickets()
    local source = source
    local Passport = vRP.Passport(source)
    
    return vRP.Query("adminSystem/getTicketsbyIdUser", {user_id = Passport})
end

function cnVRP.getUserAdvs()
    local source = source
    local Passport = vRP.Passport(source)
    
    return vRP.Query("adminSystem/getAdvsbyIdUser", {user_id = Passport})
end

function cnVRP.getUser()
    local source = source
    local Passport = vRP.Passport(source)
    
    local user = {}
    local Identity = vRP.Identity(Passport)
    
    if Identity then
        user.coins = vRP.UserGemstone(Identity.license) or 0
        user.banco = vRP.GetBank(source)
        user.rg = Identity.id
        user.telefone = Identity.phone
        user.id = Passport
        user.nome = Identity.name.." "..Identity.name2
        
        user.itens = {}
        local Inventory = vRP.Inventory(Passport)
        
        for Slot,v in pairs(Inventory) do
            if parseInt(v.amount) > 0 then
                local item = {}
                item.amount = v.amount
                item.item = v.item
                table.insert(user.itens, item)
            end
        end
        
        user.veiculos = vRP.Query("vehicles/UserVehicles",{ Passport = Passport })
        user.vip = vRP.UserPremium(Passport) > 0
        
        local permissoes = getAllPermission(Passport)
        user.permissoes = permissoes
        
        if user.vip then
            for k1,v1 in pairs(permissoes) do
                for k2,v2 in pairs(vips) do
                    if v1.permiss == v2 then
                        user.vipNome = v2
                    end
                end
            end
        end
    end
    
    return user
end

function cnVRP.searchUsers(type, value)
    local source = source
    local Passport = vRP.Passport(source)

    local offset = 0
    local rows = {}

    if type == "Identidade" and value ~= "" then
        rows = vRP.Query("adminSystem/getPlayersbyId", {offset = offset, id = parseInt(value)})
    elseif type == "Nome" and value ~= "" then
        rows = vRP.Query("adminSystem/getPlayersByName", {offset = offset, name = "%"..value.."%"})
    elseif type == "Online" then
        local players = vRP.Players()
        for Passport,source in pairs(players) do
            local Identity = vRP.Identity(Passport)
            if Identity then
                table.insert(rows, Identity)
            end
        end
    else
        rows = vRP.Query("adminSystem/getPlayers", {offset = offset})
    end

    for k,v in pairs(rows) do
        if v.id then
            local permissoes = getAllPermission(v.id)
            v.coins = vRP.UserGemstone(v.license) or 0
            v.vip = vRP.UserPremium(v.id) > 0

            if v.vip then
                for k1,v1 in pairs(permissoes) do
                    for k2,v2 in pairs(vips) do
                        if v1.permiss == v2 then
                            v.vipNome = v2
                        end
                    end
                end
            end
        end
    end

    return rows
end

function cnVRP.getItems()
    local itens = {}

    if ItemList then
        for k,v in pairs(ItemList) do
            table.insert(itens, k)
        end
    end

    return itens
end

function cnVRP.getVehicles()
    local vehicles = {}

    if VehicleList then
        for k,v in pairs(VehicleList) do
            table.insert(vehicles, k)
        end
    end

    return vehicles
end

function cnVRP.getPermissions()
    local permissions = {}

    for Permission,_ in pairs(Groups) do
        table.insert(permissions, Permission)
    end

    return permissions
end

function cnVRP.changeCoins(idUser, amount)
    local Passport = parseInt(idUser)
    local Identity = vRP.Identity(Passport)

    if Identity then
        vRP.UpgradeGemstone(Passport, parseInt(amount))
    end
end

function cnVRP.changeParkingSpaces(idUser, amount)
    local Passport = parseInt(idUser)
    -- Implementar lógica de vagas de garagem se necessário
end

function cnVRP.changeCharacters(idUser, amount)
    local Passport = parseInt(idUser)
    local Identity = vRP.Identity(Passport)

    if Identity then
        vRP.UpgradeChars(vRP.Source(Passport))
    end
end

function cnVRP.tptoUser(idUser)
    local source = source
    local Passport = parseInt(idUser)
    local targetSource = vRP.Source(Passport)

    if targetSource then
        return vCLIENT.getPositionPlayer(targetSource)
    else
        return nil
    end
end

function cnVRP.goodUser(idUser)
    local source = source
    local Passport = parseInt(idUser)
    local targetSource = vRP.Source(Passport)

    if targetSource then
        vRP.UpgradeThirst(Passport, 100)
        vRP.UpgradeHunger(Passport, 100)
        vRP.DowngradeStress(Passport, 100)
        vRP.Revive(targetSource, 200)
    end
end

function cnVRP.changeMoney(idUser, amount)
    local Passport = parseInt(idUser)
    local targetSource = vRP.Source(Passport)

    if targetSource then
        local currentBank = vRP.GetBank(targetSource)
        local difference = parseInt(amount) - currentBank

        if difference > 0 then
            vRP.GiveBank(Passport, difference)
        elseif difference < 0 then
            vRP.RemoveBank(Passport, math.abs(difference))
        end
    end
end

function cnVRP.toggleBan(idUser, expiration)
    local Passport = parseInt(idUser)
    local Identity = vRP.Identity(Passport)

    if Identity then
        local Banned = vRP.Banned(Identity.license)

        if not Banned then
            local banTime = os.time()
            if expiration > 0 then
                banTime = banTime + (expiration * 24 * 60 * 60)
            else
                banTime = banTime + (365 * 24 * 60 * 60) -- 1 ano para ban permanente
            end

            vRP.Query("banneds/InsertBanned", { license = Identity.license, time = banTime })
            vRP.Kick(vRP.Source(Passport), "Você foi banido do servidor.")
        else
            vRP.Query("banneds/RemoveBanned", { license = Identity.license })
        end
    end
end

function cnVRP.getInfoUser(idUser)
    local Passport = parseInt(idUser)
    local user = {}

    user.itens = {}
    local Inventory = vRP.Inventory(Passport)

    for Slot,v in pairs(Inventory) do
        if parseInt(v.amount) > 0 then
            local item = {
                item = v.item,
                amount = v.amount
            }
            table.insert(user.itens, item)
        end
    end

    user.veiculos = vRP.Query("vehicles/UserVehicles",{ Passport = Passport })

    local permissoes = getAllPermission(Passport)
    user.permissoes = {}

    for k,v in pairs(permissoes) do
        table.insert(user.permissoes, v.permiss)
    end

    user.id = Passport

    return user
end

function cnVRP.setPermissionUser(idUser, permission, time)
    local source = source
    local Passport = parseInt(idUser)
    local targetSource = vRP.Source(Passport)

    if targetSource then
        vRP.SetPermission(Passport, permission)
    else
        TriggerClientEvent(notify, source, "negado", "Jogador offline", 5000)
    end
end

function cnVRP.setItemUser(idUser, itemName, value)
    local source = source
    local Passport = parseInt(idUser)
    local targetSource = vRP.Source(Passport)

    if targetSource then
        vRP.GiveItem(Passport, itemName, parseInt(value), true)
    else
        TriggerClientEvent(notify, source, "negado", "Jogador offline", 5000)
    end
end

function cnVRP.setVehicleUser(idUser, vehicle)
    local Passport = parseInt(idUser)
    local Plate = vRP.GeneratePlate()

    vRP.Query("vehicles/addVehicles", { Passport = Passport, vehicle = vehicle, plate = Plate, work = false })
end

function cnVRP.removeItemUser(idUser, itemName, value)
    local source = source
    local Passport = parseInt(idUser)
    local targetSource = vRP.Source(Passport)

    if targetSource then
        if parseInt(value) == 0 then
            local Amount = vRP.InventoryItemAmount(Passport, itemName)
            vRP.TakeItem(Passport, itemName, Amount, true)
        else
            vRP.TakeItem(Passport, itemName, parseInt(value), true)
        end
    else
        TriggerClientEvent(notify, source, "negado", "Jogador offline", 5000)
    end
end

function cnVRP.removeVehicleUser(idUser, vehicle)
    local Passport = parseInt(idUser)
    vRP.Query("vehicles/removeVehicles", { Passport = Passport, vehicle = vehicle })
end

function cnVRP.removePermissionUser(idUser, permission)
    local source = source
    local Passport = parseInt(idUser)
    local targetSource = vRP.Source(Passport)

    if targetSource then
        vRP.RemovePermission(Passport, permission)
    else
        TriggerClientEvent(notify, source, "negado", "Jogador offline", 5000)
    end
end

function cnVRP.getVips()
    return Config.vips
end

function cnVRP.searchVips(vip)
    local source = source
    local rows = {}

    local players = vRP.Players()
    for Passport,source in pairs(players) do
        if vRP.HasPermission(Passport, vip) then
            local Identity = vRP.Identity(Passport)
            if Identity then
                Identity.coins = vRP.UserGemstone(Identity.license) or 0
                Identity.vip = true
                Identity.vipNome = vip
                table.insert(rows, Identity)
            end
        end
    end

    return rows
end

function cnVRP.getTickets()
    local rows = vRP.Query("adminSystem/getOpenTickets")
    local closed = vRP.Query("adminSystem/getClosedTickets")

    for k,v in pairs(closed) do
        table.insert(rows, v)
    end

    return rows
end

function cnVRP.getTicket(idTicket)
    local source = source
    local Passport = vRP.Passport(source)

    local rows = vRP.Query("adminSystem/getTicketbyId", {id = idTicket})

    for k,v in pairs(rows) do
        v.messages = vRP.Query("adminSystem/getMessagesbyTicketId", { idTicket = v.id})
    end

    return rows[1]
end

function cnVRP.createTicket(title)
    local source = source
    local Passport = vRP.Passport(source)

    vRP.Query("adminSystem/addTicket", { user_id = Passport, date = os.date("%Y-%m-%d %H:%M:%S", os.time()), title = title })

    return vRP.Query("adminSystem/getTicketsbyIdUser", {user_id = Passport})
end

function cnVRP.sendMessage(idTicket, message)
    local source = source
    local Passport = vRP.Passport(source)
    local Identity = vRP.Identity(Passport)

    if Identity then
        local name = Identity.name.." "..Identity.name2
        vRP.Query("adminSystem/addMessage", { user_id = Passport, name = name, message = message, date = os.date("%Y-%m-%d %H:%M:%S", os.time()), idTicket = idTicket })
    end
end

function cnVRP.changeStatus(idTicket, status)
    local source = source
    local Passport = vRP.Passport(source)
    vRP.Query("adminSystem/updTicketStatus", {id = idTicket, status = status})
end

function cnVRP.rateTicket(idTicket, rating)
    local source = source
    local Passport = vRP.Passport(source)
    vRP.Query("adminSystem/updTicketRate", { id = idTicket, rating = rating })
    vRP.Query("adminSystem/updTicketStatus", { id = idTicket, status = Config.closedTicketStatus})
end

function cnVRP.getSalary()
    return vRP.Query("adminSystem/getSalary")
end

function cnVRP.addSalary(perm, salary)
    vRP.Query("adminSystem/addSalary", {perm = perm, salary = salary})
    return vRP.Query("adminSystem/getSalary")
end

function cnVRP.remSalary(id)
    vRP.Query("adminSystem/remSalary", { id = id })
end

function cnVRP.searchAdvs(type, value)
    local source = source
    local Passport = vRP.Passport(source)

    local rows = nil

    if type == "Identidade" then
        rows = vRP.Query("adminSystem/getAdvsbyIdUser", {user_id = Passport})
    elseif type == "Status" then
        if value == "Expirado" then
            rows = vRP.Query("adminSystem/getExpiredAdvs")
        elseif value == "Não expirado" then
            rows = vRP.Query("adminSystem/getActiveAdvs")
        end
    end

    return rows
end

function cnVRP.addAdvUser(idUser, photo, expiration, description)
    vRP.Query("adminSystem/addAdv", {user_id = idUser, desc = description, expiration = parseInt(expiration), date = os.date("%Y-%m-%d %H:%M:%S", os.time()), photo = photo})
end

function cnVRP.announce(type, value, message)
    if type == "Identidade" then
        local targetSource = vRP.Source(parseInt(value))
        if targetSource then
            TriggerClientEvent(notify, targetSource, "azul", message, 10000)
        end
    elseif type == "Permissão" then
        if value == "Todos" then
            TriggerClientEvent(notify, -1, "azul", message, 10000)
        else
            local players = vRP.Players()
            for Passport,source in pairs(players) do
                if vRP.HasPermission(Passport, value) then
                    TriggerClientEvent(notify, source, "azul", message, 10000)
                end
            end
        end
    end
end

function cnVRP.getInfo()
    local source = source
    local Passport = vRP.Passport(source)
    local info = {}

    local Identity = vRP.Identity(Passport)
    if Identity then
        info.jogadorNome = Identity.name.." "..Identity.name2
        info.jogadorBanco = vRP.GetBank(source)
        info.jogadorRG = Identity.id
        info.jogadorTelefone = Identity.phone
        info.jogadorId = Passport
        info.jogadorPermissoes = getAllPermission(Passport)
        info.jogadorCoins = vRP.UserGemstone(Identity.license) or 0
    end

    return info
end

function cnVRP.getPlayerTickets()
    local source = source
    local Passport = vRP.Passport(source)

    local rows = {}

    if vRP.HasPermission(Passport, Config.adminPerm) then
        rows = vRP.Query("adminSystem/getOpenTickets")
    else
        rows = vRP.Query("adminSystem/getTicketsbyIdUser", {user_id = Passport})
    end

    for k,v in pairs(rows) do
        v.messages = vRP.Query("adminSystem/getMessagesbyTicketId", { idTicket = v.id})
    end

    return rows
end

function cnVRP.getLoja()
    return Config.loja
end

function cnVRP.buyItem(item, category)
    local source = source
    local Passport = vRP.Passport(source)

    if category == "vips" and loja["vips"][item] then
        local itemName = loja["vips"][item]["item"]
        local quantity = loja["vips"][item]["quantity"]
        local value = loja["vips"][item]["value"]

        local currentGems = vRP.UserGemstone(vRP.Identity(Passport).license) or 0

        if currentGems >= parseInt(value) then
            if vRP.PaymentGems(Passport, parseInt(value)) then
                vRP.GiveItem(Passport, itemName, quantity, true)
                vRP.Query("adminSystem/addPurchaseHistory", { user_id = Passport, item = itemName, category = "vips", date = os.date("%Y-%m-%d %H:%M:%S", os.time())})
                return "sucesso"
            end
        else
            TriggerClientEvent(notify, source, "negado", Config.notEnoughCoin)
            return "erro"
        end
    end

    return "erro"
end

function cnVRP.getPurchasesHistory(dateStart, dateEnd, idUser)
    local source = source
    local Passport = vRP.Passport(source)

    if idUser then
        Passport = parseInt(idUser)
    end

    if dateStart and dateEnd then
        return vRP.Query("adminSystem/getPurchasesHistorybyDate", {dateStart = dateStart, dateEnd = dateEnd})
    else
        return vRP.Query("adminSystem/getPurchasesHistory", {user_id = Passport})
    end
end

function SendWebhookMessage(webhook, message)
    if webhook ~= nil and webhook ~= "" then
        PerformHttpRequest(webhook, function(err, text, headers) end, 'POST', json.encode({content = message}), { ['Content-Type'] = 'application/json' })
    end
end

-----------------------------------------------------------------------------------------------------------------------------------------
-- SALARY SYSTEM
-----------------------------------------------------------------------------------------------------------------------------------------
RegisterServerEvent("admin_system:salary")
AddEventHandler("admin_system:salary", function()
    local source = source
    local Passport = vRP.Passport(source)

    if Passport then
        local salaries = vRP.Query("adminSystem/getSalary")

        for k,v in pairs(salaries) do
            if vRP.HasPermission(Passport, v.permission) then
                vRP.GiveBank(Passport, parseInt(v.salary))
                TriggerClientEvent(notify, source, "sucesso", Config.salaryReceived, 5000)
                break
            end
        end
    end
end)

-----------------------------------------------------------------------------------------------------------------------------------------
-- UTILITY FUNCTIONS
-----------------------------------------------------------------------------------------------------------------------------------------
function parseInt(value)
    return tonumber(value) or 0
end

function parseDate(rows)
    for k,v in pairs(rows) do
        if v.date then
            -- Converter data se necessário
            v.date = v.date
        end
    end
    return rows
end

-- Função para verificar se o jogador tem permissão para usar o sistema
function cnVRP.hasAdminPermission()
    local source = source
    local Passport = vRP.Passport(source)

    return vRP.HasPermission(Passport, Config.adminPerm)
end

-- Função para obter jogadores online
function cnVRP.getPlayersOnline(type)
    local source = source
    local Passport = vRP.Passport(source)

    local rows = {}
    local players = vRP.Players()

    if type == "Jogador" then
        for Passport,source in pairs(players) do
            local Identity = vRP.Identity(Passport)
            if Identity then
                table.insert(rows, Identity)
            end
        end
    elseif type == "Policia" then
        for Passport,source in pairs(players) do
            if vRP.HasPermission(Passport, Config.policePerm) then
                local Identity = vRP.Identity(Passport)
                if Identity then
                    table.insert(rows, Identity)
                end
            end
        end
    elseif type == "Hospital" then
        for Passport,source in pairs(players) do
            if vRP.HasPermission(Passport, Config.paramedicPerm) then
                local Identity = vRP.Identity(Passport)
                if Identity then
                    table.insert(rows, Identity)
                end
            end
        end
    elseif type == "Staff" then
        for Passport,source in pairs(players) do
            if vRP.HasPermission(Passport, Config.adminPerm) then
                local Identity = vRP.Identity(Passport)
                if Identity then
                    table.insert(rows, Identity)
                end
            end
        end
    end

    return rows
end

-- Função para obter propriedades (se necessário)
function cnVRP.getProperties()
    -- Implementar se houver sistema de propriedades
    return {}
end

-- Função para comprar propriedade (se necessário)
function cnVRP.purchaseProperty(id, apto, type)
    -- Implementar se houver sistema de propriedades
    return "erro"
end
