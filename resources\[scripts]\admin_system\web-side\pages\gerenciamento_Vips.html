
<div id="local">
   <div class="card">
      <div class="body p-3">
         <div class="row">
            <div class="col-md-10">
               <label class="form-label"><i class="far fa-star text-primary me-1"></i> VIP</label>
               <select class="form-control shadow-none" v-model="pesquisa.vip">
                  <option v-for="(permissao, index) in global.vips" :key="index" :value="permissao">{{ permissao }}</option>
               </select>
            </div>
            <div class="col-md-2 align-self-end mt-md-0 mt-2">
               <button class="btn btn-primary w-100 m-0" @click="configUsers">
                  <i class="fas fa-search"></i>
               </button>
            </div>
         </div>
      </div>
   </div>

   <!-- Lista de jogadores -->
   <div class="row">
      <div class="col-12 px-0" v-for="(u, index) in resultado" :key="index">
         <div class="card">
            <div class="body px-3 py-10">
               <div class="row">
                  <div class="col-md-8 col-lg-9 col-xl-10">
                     <h5 class="delimitarTexto font-17 mb-2">
                        {{ u.name != null && u.name2 != null ? u.name +" "+ u.name2 : 'Desconhecido' }}
                     </h5>
                     <div class="row text-muted font-12 mb-1"> 
                        <div class="col delimitarTexto">
                           <i class="far fa-hashtag text-primary font-10 normal me-1 d-none d-xl-inline"></i>
                           <small>Identidade:</small> {{ u.id != null ? u.id : '-' }} 
                        </div>
                        <div class="col delimitarTexto">
                           <i class="fab fa-steam text-primary font-10 normal me-1 d-none d-xl-inline"></i>
                           <small>Steam:</small> {{ u.steam != null ? String(u.steam).replace('steam:', '') : '-' }} 
                        </div>
                        <div class="col delimitarTexto">
                           <i class="far fa-money-bill-alt text-primary font-10 normal me-1 d-none d-xl-inline"></i>
                           <small>Banco:</small> {{ u.bank != null ? u.bank : '-' }}
                        </div>
                        <div class="col delimitarTexto">
                           <i class="far fa-coins text-primary font-10 normal me-1 d-none d-xl-inline"></i>
                           <small>Coins:</small> {{ u.gems != null ? u.gems : '-' }}
                        </div>
                     </div>
                     <div class="row text-muted font-12">
                        <div class="col delimitarTexto">
                           <i class="far fa-users text-primary font-10 normal me-1 d-none d-xl-inline"></i>
                           <small>Personagens:</small> {{ u.chars != null ? u.chars : '-'  }} 
                        </div>
                        <div class="col delimitarTexto">
                           <i class="far fa-warehouse text-primary font-10 normal me-1 d-none d-xl-inline"></i>
                           <small>Garagem:</small> {{ u.garage != null ? u.garage : '-' }} 
                        </div>
                        <div class="col delimitarTexto">
                           <i class="far fa-star text-primary font-10 normal me-1 d-none d-xl-inline"></i>
                           <small>Vip:</small> {{ JSON.parse(u.vip) == true ? u.vipNome : '-' }}
                        </div>
                        <div class="col delimitarTexto" v-if="JSON.parse(u.vip) == true">
                           <i class="far fa-calendar text-primary font-10 normal me-1 d-none d-xl-inline"></i>
                           <small>Expiração:</small> {{ new Date(parseInt(u.vipExpiracao) * 1000).toLocaleDateString('pt-BR') }}
                        </div>
                        <div class="col delimitarTexto" v-else>
                           <i class="far fa-calendar text-primary font-10 normal me-1 d-none d-xl-inline"></i>
                           <small>Expiração:</small> -
                        </div>
                     </div>
                  </div>
                  <div class="col-md-4 col-lg-3 col-xl-2 d-inline-flex align-items-center justify-content-center mt-md-0 mt-3">
                     <a class="col btn-icone text-dark" @click="tptoUser(u.id)" href="javascript:;">
                        <i class="far fa-portal-enter font-16 normal"></i><small>Teleportar</small>
                     </a>
                     <a class="col btn-icone text-dark" @click="goodUser(u.id)" href="javascript:;">
                        <i class="far fa-heart font-16 normal"></i><small>Regenerar</small>
                     </a>
                  </div>
               </div>
            </div>
         </div>
      </div>
   </div>
   <div v-if="resultado == null || resultado.length == 0">Nenhum jogador encontrado.</div>
</div>

<script>
   var local = new Vue({
      el: '#local',
      data: {
         pesquisa: {'vip': null},
         resultado: []
      }
   })

   getVips().then((data) => {
      if (global.vips[0] != null) {
         local.pesquisa.vip = global.vips[0]
         
         setTimeout(() => {
            $('select').selectpicker();
         }, 300);
      }
   })

   function configUsers() {
      searchVips(local.pesquisa.vip).then((data) => {
         local.resultado = data
      })
   }
</script>