-----------------------------------------------------------------------------------------------------------------------------------------
-- VRP
-----------------------------------------------------------------------------------------------------------------------------------------
local Tunnel = module("vrp","lib/Tunnel")
local Proxy = module("vrp","lib/Proxy")
local Config = module(GetCurrentResourceName(), 'config')

vRP = Proxy.getInterface("vRP")
vRPclient = Tunnel.getInterface("vRP")
vSURVIVAL = Tunnel.getInterface(Config.survivalModule)




-----------------------------------------------------------------------------------------------------------------------------------------
-- CONNECTION
-----------------------------------------------------------------------------------------------------------------------------------------
cnVRP = {}
Tunnel.bindInterface(GetCurrentResourceName(),cnVRP)
vCLIENT = Tunnel.getInterface(GetCurrentResourceName())



-----------------------------------------------------------------------------------------------------------------------------------------
-- PREPARES
-----------------------------------------------------------------------------------------------------------------------------------------
vRP.prepare("monkeySystem/getTicketsbyIdUser","SELECT * FROM monkey_system_tickets where user_id = @user_id and status <> 'Fechado' order by id desc")
vRP.prepare("monkeySystem/getOpenTickets","SELECT * FROM monkey_system_tickets where status = 'Aberto' order by id desc")
vRP.prepare("monkeySystem/getClosedTickets","SELECT * FROM monkey_system_tickets where status <> 'Aberto' and `date` between concat(curdate() ,' 00:00:00') and concat(curdate() , ' 23:59:59') order by id desc")
vRP.prepare("monkeySystem/getTicketbyId","SELECT * FROM monkey_system_tickets where id = @id")
vRP.prepare("monkeySystem/addTicket","INSERT INTO monkey_system_tickets (user_id, `date`, status, rating, title) VALUES(@user_id, @date, 'Aberto', '', @title);")
vRP.prepare("monkeySystem/updTicketStatus","UPDATE monkey_system_tickets SET status = @status WHERE id = @id")
vRP.prepare("monkeySystem/updTicketRate","UPDATE monkey_system_tickets SET rating = @rating WHERE id = @id")

vRP.prepare("monkeySystem/getMessagesbyTicketId","SELECT * FROM monkey_system_ticket_messages where idTicket = @idTicket order by id asc limit 20")
vRP.prepare("monkeySystem/addMessage","INSERT INTO monkey_system_ticket_messages (user_id, name, message, `date`, idTicket) VALUES(@user_id, @name, @message, @date, @idTicket);")

vRP.prepare("monkeySystem/deleteAdv","UPDATE monkey_system_adv SET deleted = 1 WHERE id = @id")
vRP.prepare("monkeySystem/getExpiredAdvs","SELECT * FROM monkey_system_adv where now() > DATE_ADD(date, INTERVAL +validity DAY) and deleted = 0 limit 25")
vRP.prepare("monkeySystem/getActiveAdvs","SELECT * FROM monkey_system_adv where now() < DATE_ADD(date, INTERVAL +validity DAY) and deleted = 0")
vRP.prepare("monkeySystem/getAdvsbyIdUser","SELECT * FROM monkey_system_adv where user_id = @user_id and deleted = 0 order by id desc")
vRP.prepare("monkeySystem/addAdv","INSERT INTO monkey_system_adv (user_id, description, validity, `date`, photo) VALUES(@user_id, @desc, @expiration, @date, @photo)")

vRP.prepare("monkeySystem/getOrganizations","SELECT * FROM monkey_system_organization")
vRP.prepare("monkeySystem/getOrganizationbyId","SELECT * FROM monkey_system_organization where id = @id")
vRP.prepare("monkeySystem/addOrganization","INSERT INTO monkey_system_organization (dateCreation, idOwner, maxMembers, name, urlPhoto) VALUES(@date, @idOwner, @maxMembers, @name, @urlPhoto)")
vRP.prepare("monkeySystem/updOrganization","UPDATE monkey_system_organization SET name = @name, urlPhoto = @urlPhoto, maxMembers = @maxMembers, idOwner = @idOwner WHERE id = @id")
vRP.prepare("monkeySystem/delOrganization","DELETE FROM monkey_system_organization WHERE id = @id")

vRP.prepare("monkeySystem/getSalary","SELECT * FROM monkey_system_salary")
vRP.prepare("monkeySystem/addSalary","INSERT INTO monkey_system_salary (permission, salary) VALUES(@perm, @salary)")
vRP.prepare("monkeySystem/remSalary","DELETE FROM monkey_system_salary WHERE id = @id")


vRP.prepare("monkeySystem/getPlayersBlacklistTrue","SELECT u.*, i.whitelist, if(sb.id != NULL, TRUE, FALSE) AS banned, i.gems, b.value AS bank FROM summerz_characters as u  join summerz_accounts as i on u.steam = i.steam left join monkey_system_organization_blacklist as m_b on m_b.user_id = u.id JOIN summerz_bank AS b ON b.user_id = u.id left JOIN summerz_banneds AS sb ON sb.steam = u.steam where m_b.id is not null and curdate() < DATE_ADD(date, INTERVAL 30 DAY) LIMIT 20 OFFSET @offset")

vRP.prepare("monkeySystem/getPlayers","SELECT u.*, i.whitelist, if(sb.id != NULL, TRUE, FALSE) AS banned, i.gems, b.value AS bank FROM summerz_characters as u  join summerz_accounts as i on u.steam = i.steam JOIN summerz_bank AS b ON b.user_id = u.id left JOIN summerz_banneds AS sb ON sb.steam = u.steam LIMIT 20 OFFSET @offset")
vRP.prepare("monkeySystem/getPlayersbyId","SELECT u.*, i.whitelist, if(sb.id != NULL, TRUE, FALSE) AS banned, i.gems, b.value AS bank FROM summerz_characters as u   join summerz_accounts as i on u.steam = i.steam  JOIN summerz_bank AS b ON b.user_id = u.id left JOIN summerz_banneds AS sb ON sb.steam = u.steam WHERE u.id = @id LIMIT 50 OFFSET @offset")
vRP.prepare("monkeySystem/getPlayersbySteam","SELECT u.*, i.whitelist, if(sb.id != NULL, TRUE, FALSE) AS banned, i.gems, b.value AS bank FROM summerz_characters as u  join summerz_accounts as i on u.steam = i.steam  JOIN summerz_bank AS b ON b.user_id = u.id left JOIN summerz_banneds AS sb ON sb.steam = u.steam WHERE u.steam = @steam LIMIT 50 OFFSET @offset")
vRP.prepare("monkeySystem/getPlayersbyPerm","SELECT u.*, i.whitelist, if(sb.id != NULL, TRUE, FALSE) AS banned, i.gems, b.value AS bank FROM summerz_characters as u  join summerz_accounts as i on u.steam = i.steam JOIN summerz_playerdata AS pd ON pd.user_id = u.id AND pd.dkey = 'Datatable' JOIN summerz_bank AS b ON b.user_id = u.id left JOIN summerz_banneds AS sb ON sb.steam = u.steam where pd.dvalue LIKE @perm LIMIT 50 OFFSET @offset")
vRP.prepare("monkeySystem/getAllPlayersbyPerm","SELECT u.*, i.whitelist,if(sb.id != NULL, TRUE, FALSE) AS banned, i.gems, b.value AS bank FROM summerz_characters as u  join summerz_accounts as i on u.steam = i.steam JOIN summerz_playerdata AS pd ON pd.user_id = u.id AND pd.dkey = 'Datatable' JOIN summerz_bank AS b ON b.user_id = u.id left JOIN summerz_banneds AS sb ON sb.steam = u.steam WHERE pd.dvalue LIKE @perm")
vRP.prepare("monkeySystem/getPlayersbyWl","SELECT u.*, i.whitelist, if(sb.id != NULL, TRUE, FALSE) AS banned, i.gems, b.value AS bank FROM summerz_characters as u  join summerz_accounts as i on u.steam = i.steam  JOIN summerz_bank AS b ON b.user_id = u.id left JOIN summerz_banneds AS sb ON sb.steam = u.steam WHERE i.whitelist = @wl  LIMIT 50 OFFSET @offset")
vRP.prepare("monkeySystem/getPlayersbyIdInfo","SELECT u.*, i.whitelist, if(sb.id != NULL, TRUE, FALSE) AS banned, i.gems, b.value AS bank, i.steam as steamInfo FROM summerz_accounts as i left join summerz_characters as u  on u.steam = i.steam JOIN summerz_bank AS b ON b.user_id = u.id left JOIN summerz_banneds AS sb ON sb.steam = u.steam WHERE i.id = @id")
vRP.prepare("monkeySystem/getVipPlayers","SELECT u.*, i.whitelist, if(sb.id != NULL, TRUE, FALSE) AS banned, i.gems, b.value AS bank, i.premium FROM summerz_characters as u  join summerz_accounts as i on u.steam = i.steam  JOIN summerz_playerdata AS pd ON pd.user_id = u.id AND pd.dkey = 'Datatable' JOIN summerz_bank AS b ON b.user_id = u.id left JOIN summerz_banneds AS sb ON sb.steam = u.steam WHERE pd.dvalue LIKE @perm and premium > 0 LIMIT 20 OFFSET @offset")

-- vRP.prepare("monkeySystem/updLastLoginUser","UPDATE summerz_characters SET lastLogin = @date WHERE id = @id")

vRP.prepare("monkeySystem/updWhitelist","UPDATE summerz_accounts SET whitelist = @wl WHERE steam = @steam")

vRP.prepare("monkeySystem/getAllPermission","SELECT * FROM summerz_playerdata AS pd WHERE pd.dvalue LIKE @perm AND pd.dkey = 'Datatable'")
vRP.prepare("monkeySystem/getPermissionDistinct","SELECT perm FROM monkey_system_permissions")

vRP.prepare("monkeySystem/getExpiredPermissions","SELECT * FROM vrp_permissions where dataExpiracao < DATE_ADD(curdate(), INTERVAL 1 DAY)")
vRP.prepare("monkeySystem/getPermissionsId","SELECT * FROM vrp_permissions WHERE user_id = @user_id")
vRP.prepare("monkeySystem/getPermission","SELECT * FROM vrp_permissions WHERE user_id = @user_id and permiss = @perm")
vRP.prepare("monkeySystem/getPermissionbyPerm","SELECT * FROM vrp_permissions WHERE permiss = @perm")
vRP.prepare("monkeySystem/addPermission","INSERT INTO vrp_permissions(user_id,permiss) VALUES(@user_id,@perm)")
vRP.prepare("monkeySystem/addPermissionExpiration","INSERT INTO vrp_permissions(user_id,permiss,dataExpiracao) VALUES(@user_id,@perm,@expiracao)")
vRP.prepare("monkeySystem/remPermission","DELETE FROM vrp_permissions WHERE user_id = @user_id AND permiss = @perm")
vRP.prepare("monkeySystem/remPermissionbyId","DELETE FROM vrp_permissions WHERE id = @id")

vRP.prepare("monkeySystem/addVehicle","INSERT IGNORE INTO summerz_vehicles(user_id,vehicle,plate,work) VALUES(@user_id,@vehicle,@plate,@work)")

vRP.prepare("monkeySystem/setRentalTime","UPDATE summerz_vehicles SET premiumtime = @premiumtime WHERE user_id = @user_id AND vehicle = @vehicle")
vRP.prepare("monkeySystem/remVehicle","DELETE FROM summerz_vehicles WHERE user_id = @user_id AND vehicle = @vehicle")

vRP.prepare("monkeySystem/getPermissionOrg","SELECT * FROM monkey_system_org_permissions WHERE idOrganization = @idOrganization")
vRP.prepare("monkeySystem/getPermissionOrgGreater","SELECT * FROM monkey_system_org_permissions WHERE idOrganization = @idOrganization and id >= (select id from monkey_system_org_permissions where name = @perm and idOrganization = @idOrganization)")
vRP.prepare("monkeySystem/addPermissionOrg","INSERT INTO monkey_system_org_permissions (idOrganization, name, permission) VALUES(@idOrganization, @name, @permission)")
vRP.prepare("monkeySystem/remPermissionOrg","DELETE FROM monkey_system_org_permissions WHERE idOrganization = @idOrganization and permission = @permission")
vRP.prepare("monkeySystem/delPermissionsOrg","DELETE FROM monkey_system_org_permissions WHERE idOrganization = @idOrganization")
vRP.prepare("monkeySystem/updNamePermissionOrg","UPDATE monkey_system_org_permissions SET name = @name WHERE idOrganization = @idOrganization and permission = @permission")

vRP.prepare("monkeySystem/addPurchaseHistory","INSERT INTO monkey_system_purchases_history (user_id, item, category, `date`) VALUES(@user_id, @item, @category, @date)")
vRP.prepare("monkeySystem/getPurchasesHistory","SELECT * FROM monkey_system_purchases_history WHERE user_id = @user_id order by id desc")
vRP.prepare("monkeySystem/getPurchasesHistorybyDate","SELECT * FROM monkey_system_purchases_history where date between @dateStart and @dateEnd  order by id desc")
vRP.prepare("monkeySystem/getLastPurchasesHistory","SELECT * FROM monkey_system_purchases_history order by id desc LIMIT 20")

vRP.prepare("monkeySystem/addBanPlayer","INSERT INTO monkey_system_banned_players (steam, validity, `date`) VALUES(@steam, @expiration, @date)")
vRP.prepare("monkeySystem/getBanPlayer","SELECT * FROM monkey_system_banned_players WHERE steam = @steam")
vRP.prepare("monkeySystem/delBanPlayer","DELETE FROM monkey_system_banned_players WHERE steam = @steam")
vRP.prepare("monkeySystem/setBanned","UPDATE summerz_accounts SET banned = @banned WHERE steam = @steam")

vRP.prepare("monkeySystem/insertBan","INSERT INTO summerz_banneds (steam, time) VALUES (@steam, @time) ")
vRP.prepare("monkeySystem/deleteBan","DELETE FROM summerz_banneds WHERE steam = @steam")


vRP.prepare("monkeySystem/getSalary","SELECT * FROM monkey_system_salary")

vRP.prepare("monkeySystem/setChars","UPDATE summerz_accounts SET chars = @chars WHERE steam = @steam")
vRP.prepare("monkeySystem/setGarages","UPDATE summerz_characters SET garage = @garage WHERE id = @id")
vRP.prepare("monkeySystem/setGems","UPDATE summerz_accounts SET gems = @gems WHERE steam = @steam")

vRP.prepare("monkeySystem/setBlacklistOrg","INSERT INTO monkey_system_organization_blacklist (user_id, `date`) VALUES(@user_id, @date)")
vRP.prepare("monkeySystem/delBlacklistOrg","DELETE FROM monkey_system_organization_blacklist WHERE user_id = @user_id")
vRP.prepare("monkeySystem/getBlacklistOrg","SELECT * FROM monkey_system_organization_blacklist where user_id = @user_id and curdate() < DATE_ADD(date, INTERVAL 30 DAY);")

vRP.prepare("monkeySystem/getHomes","SELECT * FROM homes")
vRP.prepare('monkeySystem/getHomesDetails','SELECT * FROM users_homes WHERE nome = @nome AND apartamento = @apartamento')

vRP.prepare("monkeySystem/editBank","UPDATE summerz_bank SET value = @value WHERE user_id = @user_id AND mode = 'Private'")
vRP.prepare("monkeySystem/getBank","SELECT * FROM summerz_bank WHERE user_id = @user_id  AND mode = 'Private'")

-----------------------------------------------------------------------------------------------------------------------------------------
-- CONFIG
-----------------------------------------------------------------------------------------------------------------------------------------
local logo = Config.logo
local vips = Config.vips

local loja = Config.loja
local notify = Config.notifyEvent

-----------------------------------------------------------------------------------------------------------------------------------------
-- FUNCTIONS
-----------------------------------------------------------------------------------------------------------------------------------------
function getAllPermission(user_id)
    local dataTable = vRP.getDatatable(user_id)

    local permissions = {}

    if dataTable ~= nil and dataTable["permission"] ~= nil then
        for k,v in pairs(dataTable["permission"]) do
            local obj = {}
            obj.permiss = k
            table.insert(permissions, obj)
        end
    else
        local data = vRP.userData(parseInt(user_id),"Datatable")

        if data.perm then
            for k,v in pairs(data.perm) do
                local obj = {}
                obj.permiss = k
                table.insert(permissions, obj)
            end
        end
    end

    return permissions
end

function cnVRP.getUserTickets()
    local source = source
	local user_id = vRP.getUserId(source)

    return vRP.query("monkeySystem/getTicketsbyIdUser", {user_id = user_id})
end

function cnVRP.getUserAdvs()
    local source = source
	local user_id = vRP.getUserId(source)

    return vRP.query("monkeySystem/getAdvsbyIdUser", {user_id = user_id})
end


function cnVRP.getUser()
    local source = source
	local user_id = vRP.getUserId(source)

    local user = {}

    local infoPlayer = vRP.userIdentity(user_id)
    local identity = vRP.userIdentity(user_id)

    local infoAccount = vRP.infoAccount(infoPlayer.steam)
    -- local infoAccount = vRP.query("creative/get_users", { user_id = user_id })
    local money = vRP.getBank(user_id)

    user.coins = infoAccount.gems
    user.banco = money
    user.rg = infoPlayer.registration
    user.telefone = infoPlayer.phone
    user.id = user_id
    user.nome = infoPlayer.name.." "..infoPlayer.name2
    -- user.garagens = infoAccount.garagem

    user.itens = {}
    
    local data = vRP.userData(parseInt(user_id),"Datatable")

    local itens = data["inventory"]

    for k,v in pairs(itens) do
        local item = {}
        item.amount = v.amount
        item.item = v.item

        table.insert(user.itens, item)
    end

    -- user.veiculos = vRP.query("vehicles/getVehicles",{ user_id = parseInt(user_id) })  
    user.veiculos = vRP.query("vehicles/getVehicles",{ user_id = parseInt(user_id) })  
    
    -- local steam = vRP.getSteam(source)
    -- local infos = vRP.query("vRP/get_vrp_infos",{ steam = steam })

    -- user.vipExpiracao = tostring(infoAccount.premium)
    -- user.vip = tostring(vRP.userPremium(user_id))
     user.vip = false

    local permissoes = getAllPermission(user_id)

    user.permissoes = permissoes

    if user.vip then
        for k1,v1 in pairs(permissoes) do
            for k2,v2 in pairs(vips) do
                if v1.permiss == v2 then
                    user.vipNome = v2
                end
            end
        end
    end

    return user
end

function cnVRP.searchUsers(type, value)
	-- local x = os.clock()
    local source = source
	local user_id = vRP.getUserId(source)
    
    local offset = 0
    
    local rows = {}

    if type == "Identidade" and value ~= "" then
        rows = vRP.query("monkeySystem/getPlayersbyId", {offset = offset, id = parseInt(value)})
    elseif type == "Steam" and value ~= "" then
        rows = vRP.query("monkeySystem/getPlayersbySteam", {offset = offset, steam = value})
    elseif type == "Permissão" and value ~= "" then
        rows = vRP.query("monkeySystem/getPlayersbyPerm", {offset = offset, perm = "%"..value.."%"})
    elseif type == "Whitelist" and value == "Não aprovado" then
        rows = vRP.query("monkeySystem/getPlayersbyWl", {offset = offset, wl = false})
    elseif type == "Whitelist" and value == "Aprovado" then
        rows = vRP.query("monkeySystem/getPlayersbyWl", {offset = offset, wl = true})
    elseif type == "Código de aprovação" and value ~= "" then
        rows = vRP.query("monkeySystem/getPlayersbyIdInfo", { id = value})
        rows.steam = rows.steamInfo
    elseif type == "Online" then
        local players = {}
    
        if value == "Jogador" then
            for k,v in pairs(vRP.userList()) do
                table.insert(rows,vRP.query("monkeySystem/getPlayersbyId", {offset = 0, id = k})[1])
            end
        elseif value == "Policia" then
            players = vRP.query("monkeySystem/getAllPlayersbyPerm", { perm = "%"..Config.policePerm.."%"})
        elseif value == "Hospital" then
            players = vRP.query("monkeySystem/getAllPlayersbyPerm", { perm = "%"..Config.paramedicPerm.."%" })
        elseif value == "Staff" then
            players = vRP.query("monkeySystem/getAllPlayersbyPerm", { perm = "%"..Config.adminPerm.."%" })
        end
        
        for k,v in pairs(players) do
            if vRP.getUserSource(parseInt(v.id)) ~= nil then
                table.insert(rows,v)
            end
            
        end
    else
        rows = vRP.query("monkeySystem/getPlayers", {offset = offset})
    end

    for k,v in pairs(rows) do
        -- local infos = vRP.query("vRP/get_vrp_infos",{ steam = steam })
        local permissoes = getAllPermission(user_id)

	    local infoPlayer = vRP.userIdentity(user_id)
        local infoAccount = vRP.infoAccount(infoPlayer.steam)
        -- local infoAccount = vRP.query("creative/get_users", { user_id = user_id })

        v.coins = infoAccount.gems
        v.vipExpiracao = tostring(infoAccount.premium)
        v.vip = false

        if v.vip then
            for k1,v1 in pairs(permissoes) do
                for k2,v2 in pairs(vips) do
                    if v1.permiss == v2 then
                        v.vipNome = v2
                    end
                end
            end
        end
    end
    
	-- print(string.format("elapsed time 1: %.2f\n", os.clock() - x))
    return rows
end

local cfg = module("cfg/items")

function cnVRP.getItems()
    local itens = {}

    for k,v in pairs(getItens()) do
        table.insert(itens, k)
    end

    return itens
end

function cnVRP.getVehicles()
    local vehicles = {}

    for k,v in pairs(vehicleGlobal()) do
        table.insert(vehicles, k)
    end
    
    return vehicles
end

function cnVRP.getPermissions()
    local permissions = {}

    -- for k,v in pairs(vRP.query("monkeySystem/getPermissionDistinct")) do
    --     table.insert(permissions, v.perm)
    -- end
    
    return permissions
end

function cnVRP.changeCoins(idUser, amount)
    local info = vRP.userIdentity(idUser)

    if info.steam then
        vRP.execute("monkeySystem/setGems", {gems = amount, steam = info.steam})
    end
end

function cnVRP.changeParkingSpaces(idUser, amount)
    if idUser then
        vRP.execute("monkeySystem/setGarages", {garage = amount, id = idUser})
    end
end

function cnVRP.changeCharacters(idUser, amount)
    local source = source
	-- local user_id = vRP.getUserId(source)
    -- local steam = vRP.getSteam(source)
    local info = vRP.userIdentity(idUser)

    -- if steam then
        vRP.execute("monkeySystem/setChars", {chars = amount, steam = info.steam})
    -- end
end

function cnVRP.tptoUser(idUser)
    local source = source
	local user_id = vRP.getUserId(source)

    if vRP.getUserSource(idUser) ~= nil then
        return vCLIENT.getPositionPlayer(vRP.getUserSource(idUser))
    else
        return nil
    end
end

function cnVRP.goodUser(idUser)
    local source = source
	local user_id = vRP.getUserId(source)

    local nplayer = vRP.userSource(parseInt(idUser))
    vRP.upgradeThirst(parseInt(idUser),100)
    vRP.upgradeHunger(parseInt(idUser),100)
    vRP.downgradeStress(parseInt(idUser),100)
    -- vSURVIVAL._revivePlayer(nplayer,200)
    vRPclient.revivePlayer(nplayer,200)
end

function cnVRP.changeMoney(idUser, amount)
	vRP.execute("monkeySystem/editBank",{ user_id = idUser, value = parseInt(amount)})
end

function cnVRP.toggleWhitelist(steam)
    local infoAccount = vRP.infoAccount(steam)

    if steam then
        vRP.execute("monkeySystem/updWhitelist", { wl = not infoAccount.whitelist, steam = steam })
    end
end

function cnVRP.toggleBan(idUser, expiration)
    local info = vRP.userIdentity(idUser)

    if info.steam then
        local steam = info.steam

        local rows = vRP.query("monkeySystem/getBanPlayer", { steam = steam })

        if #rows == 0 then
            if expiration == 0 then
                vRP.execute("monkeySystem/insertBan", { time = os.date("%Y-%m-%d %H:%M:%S", os.time()) ,steam = steam})
                vRP.execute("monkeySystem/addBanPlayer", { steam = steam, expiration = 9999, date = os.date("%Y-%m-%d %H:%M:%S", os.time()) })
            else
                vRP.execute("monkeySystem/insertBan", { time = os.date("%Y-%m-%d %H:%M:%S", os.time()) ,steam = steam})
                vRP.execute("monkeySystem/addBanPlayer", { steam = steam, expiration = expiration, date = os.date("%Y-%m-%d %H:%M:%S", os.time()) })
            end
            
            vRP.kick(idUser,"Você foi banido")
        else
            vRP.execute("monkeySystem/deleteBan", { steam = steam})
            vRP.execute("monkeySystem/delBanPlayer", { steam = steam})
        end
    end
end

function cnVRP.getInfoUser(idUser)
	local user_id = idUser

    local user = {}

    user.itens = {}
    -- local itens = vRP.getInventory(user_id)

    local dataTable = vRP.getDatatable(user_id)

    if dataTable ~= nil and dataTable["inventory"] ~= nil then
        local itens = dataTable["inventory"]
        
        for k,v in pairs(dataTable["inventory"]) do
            v.item = vRP.itemTransformList(v.item) or v.item
            local item = {
                item = v.item,
                amount = v.amount
            }
            table.insert(user.itens, item)
        end
    else
        local data = vRP.userData(parseInt(user_id),"Datatable")
        
        for k,v in pairs(data.inventory) do
            v.item = vRP.itemTransformList(v.item) or v.item
            local item = {
                item = v.item,
                amount = v.amount
            }
            table.insert(user.itens, item)
        end
        
    end

    user.veiculos = vRP.query("vehicles/getVehicles",{ user_id = parseInt(user_id) })

    local permissoes = getAllPermission(user_id)
    user.permissoes = {}

    for k,v in pairs(permissoes) do
        table.insert(user.permissoes, v.permiss)
    end
    
    user.id = user_id

    return user
end

-- function cnVRP.getReportsUser(idUser)
--     local user_id = idUser

--     local user = {}
--     user.advs = vRP.query("monkeySystem/getAdvsbyIdUser", {user_id = user_id})
--     user.tickets = vRP.query("monkeySystem/getTicketsbyIdUser", {user_id = user_id})
--     user.id = user_id

--     return user
-- end

function cnVRP.setPermissionUser(idUser, permission, time)
    local source = source

    local player = vRP.getUserSource(parseInt(idUser))
    if player ~= nil then
        vRP.setPermission(idUser,permission)
    else
        TriggerClientEvent(notify,source,"vermelho","Player offline",15000)
    end
end

function cnVRP.setItemUser(idUser, itemName, value)
    local source = source

    local player = vRP.getUserSource(parseInt(idUser))
    if player ~= nil then
        vRP.giveInventoryItem(idUser,itemName,value,false)
    else
        TriggerClientEvent(notify,source,"vermelho","Player offline",15000)
    end
end

function cnVRP.setVehicleUser(idUser, vehicle)
    vRP.execute("vehicles/addVehicles",{ user_id = parseInt(idUser), vehicle = vehicle, plate = vRP.generatePlate(), work = tostring(false) })
end

function generateStringNumber(format)
	local abyte = string.byte("A")
	local zbyte = string.byte("0")
	local number = ""
	for i = 1,#format do
		local char = string.sub(format,i,i)
    	if char == "D" then
    		number = number..string.char(zbyte+math.random(0,9))
		elseif char == "L" then
			number = number..string.char(abyte+math.random(0,25))
		else
			number = number..char
		end
	end
	return number
end

function generatePlateNumber()
	local user_id = nil
	local plate = ""
	repeat
		Citizen.Wait(10)
		plate = generateStringNumber("DDLLLDDD")
		user_id = getUserByPlate(plate)
	until not user_id
	return plate
end

function getUserByPlate(registration)
	local plateUser = vRP.getUserByRegistration(registration)
	if plateUser then
		return plateUser
	end
	plateUser = vRP.getOwnerByPlate(registration)
	return plateUser
end

function cnVRP.removeItemUser(idUser, itemName, value)
    local source = source

    local player = vRP.getUserSource(parseInt(idUser))
    if player ~= nil then
        if value == 0 then
            vRP.tryGetInventoryItem(idUser,itemName,vRP.getInventoryItemAmount(idUser, itemName)[1], false)
        else
            vRP.tryGetInventoryItem(idUser,itemName,value, false)
        end
    else
        TriggerClientEvent(notify,source,"vermelho","Player offline",15000)
    end
end

function cnVRP.removeVehicleUser(idUser, vehicle)
    vRP.execute("monkeySystem/remVehicle",{ user_id = parseInt(idUser), vehicle = vehicle })
end

function cnVRP.removePermissionUser(idUser, permission)
    local source = source

    local player = vRP.getUserSource(parseInt(idUser))
    if player ~= nil then
        return vRP.remPermission(idUser,permission)
    else
        TriggerClientEvent(notify,source,"vermelho","Player offline",15000)
    end
end

function cnVRP.getVips()
    return Config.vips
end

function cnVRP.searchVips(vip)
    local source = source
	local user_id = vRP.getUserId(source)

    local rows = vRP.query("monkeySystem/getVipPlayers", { offset = 0, perm = "%"..vip.."%" })

    for k,v in pairs(rows) do
        -- local steam = vRP.getSteam(source)
        -- local infos = vRP.query("vRP/get_vrp_infos",{ steam = steam })
        local permissoes = getAllPermission(user_id)

        local infoPlayer = vRP.userIdentity(user_id)
        -- local infoAccount = vRP.infoAccount(infoPlayer.steam:gsub("steam:", ""))
        local infoAccount = vRP.query("creative/get_users", { user_id = user_id })

        v.coins = infoAccount.gems
        v.vipExpiracao = tostring(infoAccount.premium)
        v.vip = tostring(true)

        if v.vip then
            for k1,v1 in pairs(permissoes) do
                for k2,v2 in pairs(vips) do
                    if v1.permiss == v2 then
                        v.vipNome = v2
                    end
                end
            end
        end
    end

    return rows
end

function cnVRP.getTickets()
    local rows = vRP.query("monkeySystem/getOpenTickets")

    local closed = vRP.query("monkeySystem/getClosedTickets")

    for k,v in pairs(closed) do
        table.insert(rows, v)
    end

    return rows
end

function cnVRP.getTicket(idTicket)
    local source = source
	local user_id = vRP.getUserId(source)

    local rows = vRP.query("monkeySystem/getTicketbyId", {id = idTicket})
    rows = parseDate(rows)

    for k,v in pairs(rows) do
        v.messages = vRP.query("monkeySystem/getMessagesbyTicketId", { idTicket = v.id})
        v.messages = parseDate(v.messages)
    end

    return rows[1]
end

function cnVRP.createTicket(title)
    local source = source
	local user_id = vRP.getUserId(source)

    vRP.execute("monkeySystem/addTicket", { user_id = user_id, date = os.date("%Y-%m-%d %H:%M:%S", os.time()), title = title })

    return vRP.query("monkeySystem/getTicketsbyIdUser", {user_id = user_id})
end

function cnVRP.sendMessage(idTicket, message)
    local source = source
	local user_id = vRP.getUserId(source)

    local infoPlayer = vRP.userIdentity(user_id)
    local name = infoPlayer.name.." "..infoPlayer.name2

    vRP.execute("monkeySystem/addMessage", { user_id = user_id, name = name, message = message, date = os.date("%Y-%m-%d %H:%M:%S", os.time()), idTicket = idTicket })
end

-- function cnVRP.tpTicket(idTicket)
--     local source = source
-- 	local user_id = vRP.getUserId(source)

--     local ticket = vRP.query("monkeySystem/getTicketbyId", {id = idTicket})

--     return vCLIENT.getPositionPlayer(vRP.getUserSource(ticket.user_id))
-- end

function cnVRP.changeStatus(idTicket, status)
    local source = source
	local user_id = vRP.getUserId(source)
    vRP.execute("monkeySystem/updTicketStatus", {id = idTicket, status = status})
end

function cnVRP.rateTicket(idTicket, rating)
    local source = source
	local user_id = vRP.getUserId(source)
    vRP.execute("monkeySystem/updTicketRate", { id = idTicket, rating = rating })
    vRP.execute("monkeySystem/updTicketStatus", { id = idTicket, status = Config.closedTicketStatus})
end

function cnVRP.getSalary()
    return vRP.query("monkeySystem/getSalary")
end

function cnVRP.addSalary(perm, salary)
    vRP.execute("monkeySystem/addSalary", {perm = perm, salary = salary})

    return vRP.query("monkeySystem/getSalary")
end

function cnVRP.remSalary(id)
    vRP.execute("monkeySystem/remSalary", { id = id })
end

function cnVRP.searchAdvs(type, value)
    local source = source
    local user_id = vRP.getUserId(source)

    local rows = nil

    if type == "Identidade" then
        rows = vRP.query("monkeySystem/getAdvsbyIdUser", {user_id = user_id})
    elseif type == "Status" then
        if value == "Expirado" then
            rows = vRP.query("monkeySystem/getExpiredAdvs")
        elseif value == "Não expirado" then
            rows = vRP.query("monkeySystem/getActiveAdvs")
        end
    end

    return rows
end

function cnVRP.addAdvUser(idUser, photo, expiration, description)
    vRP.execute("monkeySystem/addAdv", {user_id = idUser, desc = description, expiration = parseInt(expiration), date = os.date("%Y-%m-%d %H:%M:%S", os.time()), photo = photo})
end

function cnVRP.announce(type, value, message)
    if type == "Identidade" then
        local sourcePlayer = vRP.getUserSource(parseInt(value))
        if sourcePlayer ~= nil then
            TriggerClientEvent(notify,vRP.getUserSource(parseInt(value)),"azul",message,10000)
        end
    elseif type == "Permissão" then
        if value == "Todos" then
            TriggerClientEvent(notify,-1,"azul",message,10000)
        else
            local permissions = vRP.query("monkeySystem/getAllPermission", {perm = "%"..value.."%"})
            for k,v in pairs(permissions) do
                local sourcePlayer = vRP.getUserSource(parseInt(v.user_id))
                if sourcePlayer ~= nil then
                    TriggerClientEvent(notify,sourcePlayer,"azul",message,10000)
                end
            end
        end
    end
end



vRP.prepare("monkeySystem/createSiteVenda","INSERT INTO sitevenda (dataCompra, dataUltMov, formaPgto, idJogador, idUsuario, nomeUsuario, status, valorTotal) VALUES(@date, @date, @formaPgto, @idPlayer, @idSite, @name, @status, @total)")
vRP.prepare("monkeySystem/createSiteVendaProduto","INSERT INTO sitevendaproduto (categoria, foto, idJogador, nome, nomeSpawn, preco, quantidade, status, subcategoria, tipo, semEstoque) VALUES(@categoria, (SELECT f.foto FROM siteproduto as p join siteproduto_sitefoto as sp on sp.SiteProduto_id = p.id join sitefoto as f on sp.fotos_id = f.id where p.id = 5 limit 1), @idPlayer, @nome, @nomeSpawn, @preco, @qtd, @status, @subcategoria, @tipo, 0)")
vRP.prepare("monkeySystem/getSiteProduto","SELECT * FROM siteproduto where id = @id")
vRP.prepare("monkeySystem/getSiteUsuario","SELECT id FROM siteusuario where idJogador = @idPlayer order by id desc limit 1")
vRP.prepare("monkeySystem/insertRelation", "INSERT INTO sitevenda_sitevendaproduto (SiteVenda_id, produtos_id) VALUES((SELECT max(id) FROM sitevenda), (SELECT max(id) FROM sitevendaproduto))")
vRP.prepare("monkeySystem/updateQtdProduto", "UPDATE siteproduto SET estoque = estoque - 1, quantidadeVendida = quantidadeVendida + 1 WHERE id = @id")



function cnVRP.purchaseProduct(idProduct, coins)
    local source = source
	local user_id = vRP.getUserId(source)

    local idUserSite = vRP.query("monkeySystem/getSiteUsuario", {idPlayer = user_id})
    local infoPlayer = vRP.userIdentity(user_id)
    local name = infoPlayer.name.." "..infoPlayer.name2

    local product = vRP.query("monkeySystem/getSiteProduto", {id = idProduct})
    product = product[1]

    if vRP.getGmsId(user_id) >= parseInt(coins) then
        vRP.remGmsId(user_id,parseInt(coins))

        vRP.execute("monkeySystem/updateQtdProduto", {id = idProduct})

        if idUserSite[1] and idUserSite.id ~= nil then
            vRP.execute("monkeySystem/createSiteVenda", {date = os.date("%Y-%m-%d %H:%M:%S", os.time()), formaPgto = "In-game", idPlayer = user_id, idSite = idUserSite.id, name = name, status = "Finalizado", total = coins})
        else
            vRP.execute("monkeySystem/createSiteVenda", {date = os.date("%Y-%m-%d %H:%M:%S", os.time()), formaPgto = "In-game", idPlayer = user_id, idSite = nil, name = name, status = "Finalizado", total = coins})
        end

        vRP.execute("monkeySystem/createSiteVendaProduto", {categoria = product.categoria,id = idProduct, idPlayer = user_id ,nome = product.nome, nomeSpawn = product.nomeSpawn, preco = coins, qtd = 1, status = "Finalizado", subcategoria = product.subcategoria, tipo = product.tipo})
        vRP.execute("monkeySystem/insertRelation")

        local value = {}
        value.tipo = product.tipo
        value.idJogador = user_id
        value.nomeSpawn = product.nomeSpawn
        value.quantidade = product.qtd

        entregarProdutoSite(value)

        return "sucesso"
    else
        TriggerClientEvent(notify,source,"negado",Config.notEnoughCoin)
        return "erro"
    end
end

-- ############## --
-- ##   LOJA   ## --
-- ############## --

vRP.prepare("monkey/addPermissionData","INSERT INTO vrp_permissions(user_id,permiss,dataExpiracao) VALUES(@user_id,@permiss,@data + INTERVAL 30 DAY)")

function entregarProdutoSite(value)
    local user_id = value.idJogador
    local source = vRP.getUserSource(parseInt(user_id))

    if value.tipo == "veiculo" then   
        local name = value.nomeSpawn
        local identity = vRP.userIdentity(parseInt(user_id))

        if user_id then
            local vehName = tostring(name)
        
            local vehicle = vRP.query("vehicles/getVehicles",{ user_id = parseInt(user_id), vehicle = vehName })
            if vehicle[1] then
                TriggerClientEvent("Notify",source,"amarelo","Você comprou um veiculo no site que você já tem em sua garage, avise a prefeitura <b>"..vehName.."</b>.",60000)
                entregar_sucesso = true
                -- print("Carro repetido comprado")
            else
                local placa_nova = vRP.generatePlateNumber()

                vRP.execute("vRP/add_vehicle",{ user_id = parseInt(user_id), vehicle = vehName, plate = placa_nova, phone = vRP.getPhone(user_id), work = tostring(false) })
                vRP.execute("vRP/set_rental_time",{ user_id = parseInt(user_id), vehicle = vehName, premiumtime = parseInt(os.time()+30*24*60*60) })
                
                TriggerClientEvent("Notify",source,"verde","A compra no site foi concluida com sucesso seu novo veiculo já se encontra na sua garage.",60000)

                SendWebhookMessage(Config.webhook_veiculos,"```prolog\n[ID]: "..user_id.." \n[Carro VIP Entregue] "..os.date("\n[Data]: %d/%m/%Y [Hora]: %H:%M:%S").."\n[Carro VIP Entregue]: "..vehName.." \r```")
                
                entregar_sucesso = true
            end
        end
    end
    
    if value.tipo == "addMoney" then   
        local valor = value.quantidade
        
        if user_id then
            vRP.addBank(user_id,parseInt(valor))
            TriggerClientEvent("Notify",source,"verde","A compra no site foi concluida com sucesso seu dinheiro já está no seu banco.",60000)
            SendWebhookMessage(Config.webhook_money,"```prolog\n[ID]: "..user_id.." \n[Pack Monkey] "..os.date("\n[Data]: %d/%m/%Y [Hora]: %H:%M:%S").." \n[Valor]: "..valor.." \r```")
            entregar_sucesso = true
        end
    end
    
    if value.tipo == "addCoins" then   
        local valor = value.quantidade

        if user_id then
            vRP.addGmsId(user_id,parseInt(valor))
            TriggerClientEvent("Notify",source,"verde","A compra no site foi concluida com sucesso seus coins já estão disponíveis.",60000)
            SendWebhookMessage(Config.webhook_coins,"```prolog\n[ID]: "..user_id.." \n[Coins] "..os.date("\n[Data]: %d/%m/%Y [Hora]: %H:%M:%S").." \n[Valor]: "..valor.." \r```")
            entregar_sucesso = true
        end
    end
    
    if value.tipo == "vagaGarage" then   
        local qtd = value.quantidade
        local itemName = "newgarage"

        if user_id then
            vRP.giveInventoryItem(user_id,itemName,parseInt(qtd),true)
            TriggerClientEvent("Notify",source,"verde","A compra no site foi concluida com sucesso suas vagas de garage estão no seu inventário.",60000)
            SendWebhookMessage(Config.webhook_garage,"```prolog\n[ID]: "..user_id.." \n[Vagas de Garage] "..os.date("\n[Data]: %d/%m/%Y [Hora]: %H:%M:%S").." \n[QTD]: "..qtd.." \r```")
            entregar_sucesso = true
        end
    end
    
    if value.tipo == "item" then   
        local itemName = value.nomeSpawn

        if user_id then
            vRP.giveInventoryItem(user_id,itemName,1,true)
            TriggerClientEvent("Notify",source,"verde","A compra no site foi concluida com sucesso seus itens já estão no seu inventário.",60000)
            SendWebhookMessage(Config.webhook_item,"```prolog\n[ID]: "..user_id.." \n[DATA] "..os.date("\n[Data]: %d/%m/%Y [Hora]: %H:%M:%S").." \n[ITEM]: "..itemName.." \r```")
            entregar_sucesso = true
        end
    end
    
    if value.tipo == "permissao" then   
        local permissao = value.nomeSpawn

        if user_id then
            vRP.execute("monkey/addPermissionData",{ user_id = parseInt(user_id), permiss = permissao, data = os.date("%Y-%m-%d")})
            
            TriggerClientEvent("Notify",source,"verde","A compra no site foi concluida com sucesso, agora você já tem a permissão.",60000)
            SendWebhookMessage(Config.webhook_permissao,"```prolog\n[ID]: "..user_id.." \n[DATA] "..os.date("\n[Data]: %d/%m/%Y [Hora]: %H:%M:%S").." \n[PERM]: "..permissao.." \r```")
            entregar_sucesso = true
        end
    end
    
    if value.tipo == "removerBan" then
        if user_id then
            local identity = vRP.userIdentity(parseInt(user_id))

            if identity then
                vRP.execute("vRP/set_banned",{ steam = tostring(identity.steam), banned = 0 })
                SendWebhookMessage(Config.webhook_desban,"```prolog\n[ID]: "..user_id.." \n[Desbanido] "..os.date("\n[Data]: %d/%m/%Y [Hora]: %H:%M:%S").."\r```")
                entregar_sucesso = true
            end
        end
    end
end

function SendWebhookMessage(webhook,message)
	if webhook ~= nil and webhook ~= "" then
		PerformHttpRequest(webhook, function(err, text, headers) end, 'POST', json.encode({content = message}), { ['Content-Type'] = 'application/json' })
	end
end








function cnVRP.getInfo()
    -- local x = os.clock()

    local source = source
	local user_id = vRP.getUserId(source)

    local info = {} 


	local infoPlayer = vRP.userIdentity(user_id)
    if infoPlayer then
        info.jogadorNome = infoPlayer.name.." "..infoPlayer.name2
        
        local banco = vRP.query("monkeySystem/getBank",{ user_id = user_id})
        local money = banco[1].value
        info.jogadorBanco = money
    end

    local infoAccount = vRP.infoAccount(infoPlayer.steam)

    info.jogadorRG = infoPlayer.serial
    -- info.jogadorGaragens = infoAccount.garagem

    info.jogadorTelefone = infoPlayer.phone
    info.jogadorId = user_id
    info.jogadorPermissoes = getAllPermission(user_id)
    info.jogadorCoins = infoAccount.gems
    -- local infoAccount = vRP.infoAccount(infoPlayer.steam:gsub("steam:", ""))
    -- local infoAccount = vRP.query("creative/get_users", { user_id = user_id })


    return info
end

function cnVRP.getPlayerTickets()
    local source = source
	local user_id = vRP.getUserId(source)

    local rows = {}

    if vRP.hasPermission(user_id, Config.adminPerm) or vRP.hasPermission(user_id, Config.ownerPerm) then
        rows = vRP.query("monkeySystem/getOpenTickets")
    else
        rows = vRP.query("monkeySystem/getTicketsbyIdUser", {user_id = user_id})
    end

    for k,v in pairs(rows) do
        v.messages = vRP.query("monkeySystem/getMessagesbyTicketId", { idTicket = v.id})
        v.messages = parseDate(v.messages)
    end
    rows = parseDate(rows)

    return rows
end

-- function cnVRP.getPlayers(type, id, steam, perm, whitelist, page, code, online)
-- 	-- local x = os.clock()
--     local source = source
-- 	local user_id = vRP.getUserId(source)
    
--     local offset = page * 20
    
--     local rows = {}

--     print("a")

--     if type == "Identidade" and id ~= "" then
--         rows = vRP.query("monkeySystem/getPlayersbyId", {offset = offset, id = parseInt(id)})
--     elseif type == "Steam" and steam ~= "" then
--         rows = vRP.query("monkeySystem/getPlayersbySteam", {offset = offset, steam = "steam:"..steam})
--     elseif type == "Permissão" and perm ~= "" then
--         rows = vRP.query("monkeySystem/getPlayersbyPerm", {offset = offset, perm = perm})
--     elseif type == "Whitelist" and whitelist == "Não aprovado" then
--         rows = vRP.query("monkeySystem/getPlayersbyWl", {offset = offset, wl = false})
--     elseif type == "Whitelist" and whitelist == "Aprovado" then
--         rows = vRP.query("monkeySystem/getPlayersbyWl", {offset = offset, wl = true})
--     elseif type == "Código de aprovação" and code ~= "" then
--         rows = vRP.query("monkeySystem/getPlayersbyIdInfo", { id = code})
--         rows.steam = rows.steamInfo
--     elseif type == "Online" then
--         print("b")
--         local players = {}
    
--         print("c")
--         if online == "Jogador" then
--             for k,v in pairs(vRP.getUsersByPermission("")) do
--                 table.insert(rows,vRP.query("monkeySystem/getPlayersbyId", {offset = 0, id = k})[1])
--             end
--         elseif online == "Policia" then
--             players = vRP.query("monkeySystem/getAllPlayersbyPerm", { perm = Config.policePerm})
--         elseif online == "Hospital" then
--             players = vRP.query("monkeySystem/getAllPlayersbyPerm", { perm = Config.paramedicPerm})
--         elseif online == "Staff" then
--             players = vRP.query("monkeySystem/getAllPlayersbyPerm", { perm = Config.adminPerm})
--         end
        
--         print("d")
--         for k,v in pairs(players) do
--             if vRP.getUserSource(parseInt(v.id)) ~= nil then
--                 table.insert(rows,v)
--             end
--         end
--         print("e")
--     elseif type == "Blacklist facções" then
--         rows = vRP.query("monkeySystem/getPlayersBlacklistTrue", {offset = offset, id = parseInt(id)})
--     else
--         rows = vRP.query("monkeySystem/getPlayers", {offset = offset})
--     end

--     for k,v in pairs(rows) do
--         if v.lastLogin ~= 0 then
--             local date = os.date("*t", tostring(parseInt(v.lastLogin)):gsub("000", ""))
--             v.lastLogin = date.hour..":"..date.min.." "..date.day.."/"..date.month.."/"..date.year
--         end
--     end
    
--     print(rows)
-- 	-- print(string.format("elapsed time 1: %.2f\n", os.clock() - x))
--     return rows
-- end

function cnVRP.getAdvUser(idUser)
    local rows = vRP.query("monkeySystem/getAdvsbyIdUser", {user_id = idUser})

    -- rows = parseDate(rows)

    return rows
end

-- function cnVRP.getItens(idUser)
--     local source = source
-- 	local user_id = vRP.getUserId(source)

--     local itens = {}
--     itens.inventory = vRP.getInventory(idUser)
--     itens.vehicle = vRP.query("vehicles/getVehicles",{ user_id = parseInt(idUser) })  

--     return itens
-- end

function cnVRP.setBlacklist(steam)
    local source = source
	local user_id = vRP.getUserId(source)

    if steam then
        local rows = vRP.query("monkeySystem/getPlayersbyId", {offset = 0, id = parseInt(user_id)})

        if rows.blacklist == 1 then
            vRP.execute("monkeySystem/delBlacklistOrg", { user_id = user_id })
        else
            vRP.execute("monkeySystem/setBlacklistOrg", { user_id = user_id, date = os.date("%Y-%m-%d")})
        end
    end

end


-- function cnVRP.getPermissions(idUser)
--     local rows = vRP.query("monkeySystem/getPermissionsId",{ user_id = parseInt(idUser) })

--     for k,v in pairs(rows) do
--         if v.dataExpiracao ~= 0 then
--             local date = os.date("*t", tostring(parseInt(v.dataExpiracao)):gsub("000", ""))
--             v.dataExpiracao = date.hour..":"..date.min.." "..date.day.."/"..date.month.."/"..date.year
--         else
--             v.dataExpiracao = nil
--         end
--     end

--     return rows
-- end


function cnVRP.getOrganizations()
    local rows = vRP.query("monkeySystem/getOrganizations")

    for k,v in pairs(rows) do
        local perms = vRP.query("monkeySystem/getPermissionsIdOrganizationQtd", { idOrganization = v.id } )

        v.members = #perms
        if v.dateCreation ~= 0 then
            local date = os.date("*t", tostring(parseInt(v.dateCreation)):gsub("000", ""))
            v.dateCreation = date.hour..":"..date.min.." "..date.day.."/"..date.month.."/"..date.year
        else
            v.dateCreation = nil
        end
    end

    return rows
end

function cnVRP.getMyOrganizations()
    local source = source
	local user_id = vRP.getUserId(source)

    local rows = vRP.query("monkeySystem/getPermissionsOrganizationsUser",{ user_id = user_id })
    local organizations = {}

    for k,v in pairs(rows) do
        
        local org = vRP.query("monkeySystem/getOrganizationbyId", { id = v.idFaccao })
        if org.dateCreation ~= 0 then
            local date = os.date("*t", tostring(parseInt(org.dateCreation)):gsub("000", ""))
            org.dateCreation = date.hour..":"..date.min.." "..date.day.."/"..date.month.."/"..date.year
        else
            org.dateCreation = nil
        end
        
        local members = vRP.query("monkeySystem/getPermissionsIdOrganizationGroup", { idOrganization = v.idFaccao })
        local playersOnline = {}
        for k,v in pairs(members) do
            if vRP.getUserSource(parseInt(v.user_id)) ~= nil then
                table.insert(playersOnline, v.user_id)
            end
        end

        org.members = #members
        org.onlineMembers = #playersOnline
        table.insert(organizations, org[1])
    end

    return organizations
end

function cnVRP.getOrganizationById(id)
    -- local x = os.clock()

    local source = source
	local user_id = vRP.getUserId(source)

    local organization = vRP.query("monkeySystem/getOrganizationbyId", { id = id })

    for k,v in pairs(organization) do
		local date = os.date("*t", tostring(parseInt(v.dateCreation)):gsub("000", ""))
		v.dateCreation = date.hour..":"..date.min.." "..date.day.."/"..date.month.."/"..date.year
    end

    local members = vRP.query("monkeySystem/getPermissionsIdOrganizationGroup", { idOrganization = id })
    local auxiliarMembersId = {}
    local positionsWithOP = vRP.query("monkeySystem/getPermissionsIdandIdOrganization", { user_id = user_id, idOrganization = id })

    local positions = vRP.query("monkeySystem/getPermissionOrg", { idOrganization = id })
    local playersOnline = {}

    for k,v in pairs(members) do
        if vRP.getUserSource(parseInt(v.user_id)) ~= nil then
            table.insert(playersOnline, v.user_id)
        end
    end

    for k,v in pairs(positions) do
        local members = vRP.query("monkeySystem/getPermissionbyPermOrganization", { perm = v.permission, idOrganization = id })
        local positionsTemp = {}
        local isOp = false
        local isMe = false
        
        if #auxiliarMembersId > 0 then
            for k2,v2 in pairs(members) do
                local found = false
                for k3,v3 in pairs(auxiliarMembersId) do
                    if v2.user_id == parseInt(v3) then
                        found = true
                    end
                    if v2.user_id == user_id then
                        isMe = true
                    end
                end
                if not found then
                    table.insert(positionsTemp, v2)
                    table.insert(auxiliarMembersId, v2.user_id)
                else
                    if isMe == true then
                        isOp = true
                    end
                end
            end
        else
            for k2,v2 in pairs(members) do
                table.insert(positionsTemp, v2)
                table.insert(auxiliarMembersId, v2.user_id)
            end
        end
        
        local temp = {}
        if positionsTemp and positionsTemp[1] then
            -- print("DUMP 2")
            -- print(Dump(positionsTemp))
            for k,v in pairs(positionsTemp) do
                -- print("DUMP 3")
                -- print(Dump(v))
                local info = vRP.userIdentity(v.user_id)

                for k,v in pairs(info) do
                    local date = os.date("*t", tostring(parseInt(v.lastLogin)):gsub("000", ""))
                    v.lastLogin = date.hour..":"..date.min.." "..date.day.."/"..date.month.."/"..date.year
                end

                table.insert(temp, info[1])
                
                -- print("temp ")
                -- print(Dump(temp))
            end
        end

        if organization.idOwner == user_id or vRP.hasPermission(user_id, Config.adminPerm) then
            isOp = true
        end
        
        v.members = temp
        v.isOp = isOp
    end

    organization.members = #members
    organization.positions = positions
    organization.playersOnline = playersOnline

    -- print(string.format("elapsed time 1: %.2f\n", os.clock() - x))

    return organization

end

function cnVRP.addMemberOrganization(idOrganization, permission, idPlayer)
    local org = vRP.query("monkeySystem/getOrganizationbyId", {id = idOrganization} )
    local members = vRP.query("monkeySystem/getPermissionsIdOrganizationGroup", { idOrganization = idOrganization })
    local blacklist = vRP.query("monkeySystem/getBlacklistOrg", { user_id = idPlayer })

    
    local info = vRP.userIdentity(idPlayer)

    if #members < org.maxMembers and #blacklist == 0 and info[1] ~= nil then
        local rows = vRP.query("monkeySystem/getPermission", { user_id = idPlayer, perm = permission })
        if #rows == 0 then
            local perms = vRP.query("monkeySystem/getPermissionOrgGreater", { perm = permission, idOrganization = idOrganization })

            for k,v in pairs(perms) do
                vRP.execute("monkeySystem/addPermissionOrganization", { user_id = idPlayer, perm = v.permission, idFaccao = idOrganization})
            end
            return true
        else
            return false
        end
    else
        return false
    end
end

function cnVRP.removeMemberOrganization(idOrganization, idPlayer)
    if idOrganization and idPlayer then
        vRP.execute("monkeySystem/remPermissionOrganization", { user_id = idPlayer, idFaccao = idOrganization })
        vRP.execute("monkeySystem/setBlacklistOrg", { user_id = idPlayer, date = os.date("%Y-%m-%d")})
    end
end

function cnVRP.addRole(idOrganization, permission)
    vRP.execute("monkeySystem/addPermissionOrg", { idOrganization = idOrganization, name = permission, permission = permission })  

    local members = vRP.query("monkeySystem/getPermissionsIdOrganizationGroup", { idOrganization = idOrganization })
    for k,v in pairs(members) do
        vRP.execute("monkeySystem/addPermissionOrganization", { user_id = v.user_id, perm = permission, idFaccao = idOrganization})
    end
end

function cnVRP.removeRole(idOrganization, permission)
    vRP.execute("monkeySystem/remPermissionOrg", { idOrganization = idOrganization, permission = permission })

    -- local rows = vRP.query("monkeySystem/getPermissionbyPerm", { perm = permission})
    local rows = vRP.query("monkeySystem/getPermissionbyPermOrganization", { perm = permission, idOrganization = idOrganization })

    for k,v in pairs(rows) do
        vRP.execute("monkeySystem/remPermissionOrganization", { user_id = v.user_id, idFaccao = idOrganization})
    end
end

function cnVRP.renameRole(idOrganization, permission, name)
    vRP.execute("monkeySystem/updNamePermissionOrg", { name = name, idOrganization = idOrganization, permission = permission })  
end

function cnVRP.addOrganization(urlLogo, name, maxMembers, idOwner)
    vRP.execute("monkeySystem/addOrganization", { date = os.date("%Y-%m-%d %H:%M:%S", os.time()), idOwner = idOwner, maxMembers = maxMembers, name = name, urlPhoto = urlLogo})
end

function cnVRP.editOrganization(idOrganization, urlLogo, name, maxMembers, idOwner)
    vRP.execute("monkeySystem/updOrganization", { id = idOrganization, name = name, urlPhoto = urlLogo, maxMembers = maxMembers, idOwner = idOwner })
end

function cnVRP.deleteOrganization(idOrganization)
    vRP.execute("monkeySystem/delOrganization", { id = idOrganization })
    vRP.execute("monkeySystem/delPermissionsOrg", { idOrganization = idOrganization })
    vRP.execute("monkeySystem/delPermissionOrganization", { idOrganization = idOrganization })
end


function cnVRP.getLoja()
    loja.vehicles = {}

    for k,v in pairs(vehicleGlobal()) do
        if v[4] == Config.carsStore then
            local car = {
                ["item"] = k,
                ["description"] = v[1],
                ["value"] = v[3],
                ["quantity"] = 1
            }
            table.insert(v,k)
            loja.vehicles[v[1]] = car
        end
    end

    return loja
end

function cnVRP.buyItem(item, category)
    local source = source
	local user_id = vRP.getUserId(source)

    if category == "cars" then
        for k,v in pairs(vehicleGlobal()) do
            if k == item then
                if v[4] == "donate" then 
                    if vRP.getGmsId(user_id) >= parseInt(v[3]) then
                        vRP.remGmsId(user_id,parseInt(v[3]))
                        vRP.execute("monkeySystem/addVehicle",{ user_id = parseInt(user_id), vehicle = k, plate = vRP.generatePlateNumber(), phone = vRP.getPhone(user_id), work = tostring(false) })
                        vRP.execute("monkeySystem/setRentalTime",{ user_id = parseInt(user_id), vehicle = k, premiumtime = parseInt(os.time()+30*24*60*60) })

                        vRP.execute("monkeySystem/addPurchaseHistory", { user_id = user_id, item = k, category = "cars", date = os.date("%Y-%m-%d %H:%M:%S", os.time())})
                    else
                        TriggerClientEvent(notify,source,"negado",Config.notEnoughCoin) -- COLOCAR NO CONFIG
                    end
                elseif vRP.paymentBank(user, parseInt(v[3])) then
                    vRP.execute("monkeySystem/addVehicle",{ user_id = parseInt(user_id), vehicle = k, plate = vRP.generatePlateNumber(), phone = vRP.getPhone(user_id), work = tostring(false) })
                    vRP.execute("monkeySystem/setRentalTime",{ user_id = parseInt(user_id), vehicle = k, premiumtime = parseInt(os.time()+30*24*60*60) })

                    vRP.execute("monkeySystem/addPurchaseHistory", { user_id = user_id, item = k, category = "cars", date = os.date("%Y-%m-%d %H:%M:%S", os.time())})
                else
                    TriggerClientEvent(notify,source,"negado",Config.notEnoughMoney) -- COLOCAR NO CONFIG
                end
            end
        end
    elseif category == "vips" then

        local itemName = loja["vips"][item]["item"]
        local quantity = loja["vips"][item]["quantity"]
        local value = loja["vips"][item]["value"]
    
        if vRP.getGmsId(user_id) >= parseInt(value) then
            vRP.remGmsId(user_id,parseInt(value))
            vRP.giveInventoryItem(user_id,itemName,quantity,false)

            vRP.execute("monkeySystem/addPurchaseHistory", { user_id = user_id, item = itemName, category = "vips", date = os.date("%Y-%m-%d %H:%M:%S", os.time())})
        else
            TriggerClientEvent(notify,source,"negado",Config.notEnoughCoin)
        end
    end
end

function cnVRP.getPurchasesHistory(dateStart, dateEnd, idUser)
    local source = source
	local user_id = vRP.getUserId(source)
    
    if vRP.hasPermission(user_id, Config.adminPerm) or vRP.hasPermission(user_id, Config.ownerPerm) then
        if idUser then
            return parseDate(vRP.query("monkeySystem/getPurchasesHistory", { user_id = user_id }))
        elseif dateStart and dateEnd then
            return parseDate(vRP.query("monkeySystem/getPurchasesHistorybyDate", { dateStart = dateStart, dateEnd = dateEnd }))
        end
        return parseDate(vRP.query("monkeySystem/getLastPurchasesHistory"))
    else
        return parseDate(vRP.query("monkeySystem/getPurchasesHistory", { user_id = user_id }))
    end
end



function cnVRP.getPlayersOnline(type)    
    local source = source
	local user_id = vRP.getUserId(source)
    
    local offset = page * 20
    
    local rows,players = {}, {}

    if type == "Jogador" then
        for k,v in pairs(vRP.userList()) do
            table.insert(rows,vRP.query("monkeySystem/getPlayersbyId", {offset = 0, id = k}))
        end
    elseif type == "Policia" then
        players = vRP.query("monkeySystem/getAllPlayersbyPerm", { perm = Config.policePerm})
    elseif type == "Hospital" then
        players = vRP.query("monkeySystem/getAllPlayersbyPerm", { perm = Config.paramedicPerm})
    elseif type == "Staff" then
        players = vRP.query("monkeySystem/getAllPlayersbyPerm", { perm = Config.adminPerm})
    end
    
    for k,v in pairs(players) do
        if vRP.getUserSource(parseInt(v.user_id)) ~= nil then
            table.insert(rows,v.user_id)
        end
    end

    for k,v in pairs(rows) do
        if v.lastLogin ~= 0 then
            local date = os.date("*t", tostring(parseInt(v.lastLogin)):gsub("000", ""))
            v.lastLogin = date.hour..":"..date.min.." "..date.day.."/"..date.month.."/"..date.year
        end
    end
end

function cnVRP.getOnline()
    local staff = {}
    local jogador = {}
    local policia = {}
    local hospital = {}

    local permissions = vRP.query("monkeySystem/getAllPermission", {perm = "%"..Config.adminPerm.."%"})

    for k,v in pairs(permissions) do
        if vRP.getUserSource(parseInt(v.user_id)) ~= nil then
            if table.contains(staff, v.user_id) == false then
                table.insert(staff, v.user_id)
            end
        end
    end

    permissions = vRP.query("monkeySystem/getAllPermission", {perm = "%"..Config.policePerm.."%"})

    for k,v in pairs(permissions) do
        if vRP.getUserSource(parseInt(v.user_id)) ~= nil then
            table.insert(policia, v.user_id)
        end
    end

    permissions = vRP.query("monkeySystem/getAllPermission", {perm = "%"..Config.paramedicPerm.."%"})

    for k,v in pairs(permissions) do
        if vRP.getUserSource(parseInt(v.user_id)) ~= nil then
            table.insert(policia, v.user_id)
        end
    end

    for k,v in pairs(vRP.userList()) do
        table.insert(jogador, k)
    end

    local info = {}
    info.staff = staff
    info.jogador = jogador
    info.policia = policia
    info.hospital = hospital

    return info
end

function cnVRP.getProperties()
    local homes = vRP.query("monkeySystem/getHomes")
    local casas = {}

    for k, v in pairs(homes) do
        if v.apartamento == -1 then
            local homeDetails = vRP.query('monkeySystem/getHomesDetails', {nome = v.nome, apartamento = -1})
            
            if #homeDetails == 0 then
                local home = {}
                home.homeValue = v.valor
                home.homeName = v.nome
                home.homeApto = v.apartamento
                home.homeId = v.id
                home.homePhoto = v.foto
                home.homeChestWeight = v.bau
                home.homeKeyAmount = v.qtd_chaves
                home.homeValueCoins = v.coins

                table.insert(casas, home)
            end
        else
            for i=1,v.apartamento do
                local homeDetails = vRP.query('monkeySystem/getHomesDetails', {nome = v.nome, apartamento = i})
                
                if #homeDetails == 0 then
                    local home = {}
                    home.homeApto = i
                    home.homeValue = v.valor
                    home.homeName = v.nome
                    home.homeId = v.id
                    home.homePhoto = v.foto
                    home.homeChestWeight = v.bau
                    home.homeKeyAmount = v.qtd_chaves
                    home.homeValueCoins = v.coins
    
                    table.insert(casas,home)
                end
            end
        end
    end

    return casas
end

function cnVRP.purchaseProperty(id, apto, type)
    local source = source
	local user_id = vRP.getUserId(source)

    local home = vRP.query("vRP/getHomesById", {id = id})

    local homeDetails = vRP.query('vRP/get_home_details', {nome = home.nome, apartamento = apto})

    if #homeDetails > 0 then
        if homeDetails.user_id == user_id then
            TriggerClientEvent('Notify',source,'importante','Essa casa já é sua.',15000)
        else
            TriggerClientEvent('Notify',source,'negado','Essa casa não está disponível para venda pela imobiliária.',15000)
        end
    else
        local casa_comprada = vRP.query('vRP/get_home_details', {nome = home.nome, apartamento = apto})
        if #casa_comprada > 0 then
            TriggerClientEvent('Notify',source,'negado','Essa casa já foi comprada.',15000) 
            return
        else
            if type == "Dinheiro" then
                if vRP.paymentBank(user_id,parseInt(home.valor)) then
                    local iptu = calculaDias(Config.diasIPTU)
                    local expire_home = calculaDias(Config.diasIPTU + Config.diasRemoverHome)
                    vRP.execute('vRP/buy_home', {
                        ['user_id'] = user_id,
                        ["nome"] = home.nome,
                        ["interior"] = Config.upgrades[home.interior].casas.i,
                        ["apartamento"] = apto,
                        ["iptu"] = iptu,
                        ["expire_home"] = expire_home,
                        ["isOwner"] = true,
                        ['bau'] = Config.upgrades[home.interior].init_bau,
                        ['qtd_chaves'] = Config.upgrades[home.interior].init_chaves
                    })
                    vRP.execute('vRP/insert_perm', {
                        ['user_id'] = user_id,
                        ["nome"] = home.nome,
                        ["apartamento"] = apto
                    }) 
                    vRP.execute('vRP/update_disp', {
                        ['disponivel'] = home.disponivel - 1,
                        ['nome'] = home.nome
                    })

                    return "sucesso"
                else
                    return "erro"
                end
            elseif type == "Coins" then
                if vRP.remGmsId(user_id,parseInt(home.coins)) then
                    local iptu = calculaDias(Config.diasIPTU)
                    local expire_home = calculaDias(Config.diasIPTU + Config.diasRemoverHome)
                    vRP.execute('vRP/buy_home', {
                        ['user_id'] = user_id,
                        ["nome"] = home.nome,
                        ["interior"] = Config.upgrades[home.interior].casas.i,
                        ["apartamento"] = apto,
                        ["iptu"] = iptu,
                        ["expire_home"] = expire_home,
                        ["isOwner"] = true,
                        ['bau'] = Config.upgrades[home.interior].init_bau,
                        ['qtd_chaves'] = Config.upgrades[home.interior].init_chaves
                    })
                    vRP.execute('vRP/insert_perm', {
                        ['user_id'] = user_id,
                        ["nome"] = home.nome,
                        ["apartamento"] = apto
                    }) 
                    vRP.execute('vRP/update_disp', {
                        ['disponivel'] = home.disponivel - 1,
                        ['nome'] = home.nome
                    })

                    return "sucesso"
                else
                    return "erro"
                end
            end
        end
    end
end




function cnVRP.isAdmin()
    local source = source
	local user_id = vRP.getUserId(source)
    
    return vRP.hasPermission(user_id, Config.adminPerm)
end

function dump(o)
    if type(o) == 'table' then
        local s = '{ '
        for k, v in pairs(o) do
            if type(k) ~= 'number' then k = '"' .. k .. '"' end
            s = s .. '[' .. k .. '] = ' .. dump(v) .. ','
        end
        return s .. '} '
    else
        return tostring(o)
    end
end

function parseDate(obj)
    for k,v in pairs(obj) do
		local date = os.date("*t", tostring(parseInt(v.date)):gsub("000", ""))
		v.date = date.hour..":"..date.min.." "..date.day.."/"..date.month.."/"..date.year
    end
    return obj
end

function table.contains(table, element)
    for _, value in pairs(table) do
      if value == element then
        return true
      end
    end
    return false
  end

  -------------------------------------------------------------------------------------------------
--[ CALCULA OS DIAS ]----------------------------------------------------------------------------
-------------------------------------------------------------------------------------------------
function calculaDias (qdias)
	local stimer = parseInt(os.time()+(24*qdias*60*60))
	
	return stimer	
end

-----------------------------------------------------------------------------------------------------------------------------------------
-- EVENTS
-----------------------------------------------------------------------------------------------------------------------------------------
-- AddEventHandler("vRP:playerLeave",function(user_id,source)
--     -- print(os.date("%Y-%m-%d %H:%M:%S", os.time()))
-- 	vRP.execute("monkeySystem/updLastLoginUser", { id = user_id, date = os.date("%Y-%m-%d %H:%M:%S", os.time())})
-- end)

-- AddEventHandler("vRP:playerSpawn",function(user_id,source)
--     -- print(os.date("%Y-%m-%d %H:%M:%S", os.time()))
-- 	vRP.execute("monkeySystem/updLastLoginUser", { id = user_id, date = os.date("%Y-%m-%d %H:%M:%S", os.time())})
-- end)

RegisterNetEvent("monkey_system_v2:salary")
AddEventHandler("monkey_system_v2:salary",function()
	local source = source
	local user_id = vRP.getUserId(source)

    local salary = vRP.query("monkeySystem/getSalary")
    local salarySum = 0.0

    for k, v in pairs(salary) do
        if vRP.hasPermission(parseInt(user_id), v.permission) then
            salarySum = salarySum + parseInt(v.salary)
        end
    end
    if salarySum > 0.0 then
        vRP.setSalary(parseInt(user_id), salarySum)
        TriggerClientEvent(notify, source, "sucesso", Config.salaryReceived, 5000)
    end
end)

-----------------------------------------------------------------------------------------------------------------------------------------
-- THREADS
-----------------------------------------------------------------------------------------------------------------------------------------
Citizen.CreateThread(function()
    
    local rows = vRP.query("monkeySystem/getExpiredAdvs")

    for k,v in pairs(rows) do
        vRP.execute("monkeySystem/deleteAdv", { id = v.id })
    end
   
end)

Citizen.CreateThread(function()
    while true do
        TriggerClientEvent("monkey:modulos", -1)
        TriggerEvent("monkey:modulos")
        Citizen.Wait(15000)
    end
end)

function dump(o)
	if type(o) == 'table' then
		local s = '{ '
		for k, v in pairs(o) do
			if type(k) ~= 'number' then k = '"' .. k .. '"' end
			s = s .. '[' .. k .. '] = ' .. dump(v) .. ','
		end
		return s .. '} '
	else
		return tostring(o)
	end
end