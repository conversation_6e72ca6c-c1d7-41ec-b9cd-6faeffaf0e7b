<div id="local" class="row clearfix">
   <div class="col-lg-12 px-0" v-if="jogador != null && jogador.id != null">
      <div class="card">
         <div class="body widget-user">
            <div class="text-right" style="float: right;">
               <h2 class="m-b-0 text-center">{{ jogador.id }}</h2>
               <small class="info">Identidade</small>
            </div>
            <img src="images/man.png" alt="Avatar">
            <div class="wid-u-info col-7">
               <h5 class="mb-2">{{ jogador.nome }}</h5>
               <p class="text-muted m-b-0"> 
                  <i class="far fa-phone-alt text-primary me-1"></i> {{ jogador.telefone }} 
                  <span class="ms-5"><i class="far fa-address-card text-primary me-1"></i> {{ jogador.rg }}</span>
               </p>
            </div>
         </div>
      </div>

      <div class="card mb-0">
         <div class="row m-0 profile_state">
            <div class="col-md-4 col-6 mb-2">
               <div class="body p-3">
                  <h4 class="mt-0 mb-1">{{ jogador.coins }}</h4>
                  <p class="mt-1 mb-0"><i class="far fa-coin text-warning me-1"></i> Coins</p>
               </div>
            </div>
            <div class="col-md-4 col-6 mb-2">
               <div class="body p-3">
                  <h4 class="mt-0 mb-1">{{ jogador.banco }}</h4>
                  <p class="mt-1 mb-0"><i class="far fa-money-bill-wave-alt text-success me-1"></i> Banco</p>
               </div>
            </div>
            <div class="col-md-4 col-6 mb-2">
               <div class="body p-3">
                  <h4 class="mt-0 mb-1" v-if="JSON.parse(jogador.vip) == true">{{ new Date(parseInt(jogador.vipExpiracao) * 1000).toLocaleDateString('pt-BR') }}</h4>
                  <h4 class="mt-0 mb-1" v-else>Não possui VIP</h4>
                  <p class="mt-1 mb-0"><i class="far fa-star text-info me-1"></i> Vip {{ JSON.parse(jogador.vip) == true ? ' ('+ jogador.vipNome +')' : '' }}</p>
               </div>
            </div>
            <!-- <div class="col-md-3 col-6 mb-2">
               <div class="body p-3">
                  <h4 class="mt-0 mb-1">{{ jogador.garagens }}</h4>
                  <p class="mt-1 mb-0"><i class="far fa-garage text-danger me-1"></i> Garagens</p>
               </div>
            </div> -->
         </div>
      </div>

      <div class="card">
         <div class="row m-0 profile_state">
            <div class="col-md-6 mb-2">
               <div class="body">
                  <h4 class="mt-0 mb-3">Itens</h4>

                  <div class="cards">
                     <div class="body text-center p-2" v-for="(i, index) in jogador.itens" :key="index">
                        <img class="mb-2" :src="global.urlFotoItens + i.item +'.png'" @error="imageError" /><br>
                        <h6 class="m-0 delimitarTexto">{{ i.item }}</h6>
                        <small>{{ i.amount }} <small>UN</small></small>
                     </div>
                  </div>

                  <div v-if="jogador.itens == null || jogador.itens.length == 0">Nenhum item encontrado.</div>
               </div>
            </div>
            <div class="col-md-6 mb-2">
               <div class="body">
                  <h4 class="mt-0 mb-3">Veículos</h4>

                  <div class="cards">
                     <div class="body text-center p-2" v-for="(v, index) in jogador.veiculos" :key="index">
                        <img class="mb-2" :src="global.urlFotoVeiculos + v.vehicle +'.png'" @error="imageError" /><br>
                        <h6 class="m-0 delimitarTexto">{{ v.vehicle }}</h6>
                        <small><i class="far fa-car-side text-primary me-1"></i>{{ v.body }}<small>%</small></small>
                        <small class="ms-2"><i class="far fa-gas-pump text-primary me-1"></i>{{ v.fuel }}<small>%</small></small><br>
                        <small><i class="far fa-car-battery text-primary me-1"></i>{{ parseInt(v.engine) / 10 }}<small>%</small></small>
                     </div>
                  </div>

                  <div v-if="jogador.veiculos == null || jogador.veiculos.length == 0">Nenhum veículo encontrado.</div>
               </div>
            </div>
         </div>
      </div>
   </div>
   <div v-else>Nenhuma informação encontrada.</div>
</div>

<script>
   var local = new Vue({
      el: '#local',
      data: {
         jogador: {}
      }
   })

   getUser().then((data) => {
      local.jogador = data
   })
</script>