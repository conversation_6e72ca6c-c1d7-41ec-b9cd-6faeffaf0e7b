@import url('https://fonts.googleapis.com/css2?family=Rubik&display=swap');

.corpo {
   position: fixed;
	top: 50%;
	left: 50%;
   transform: translate(-50%, -50%);
	height: 80vh;
	width: 80vw;
	max-width: 1920px;
	max-height: 1080px;
   border: 5px solid #000;
   background-color: #e1e7f0;
   background: -moz-linear-gradient(left,  rgba(255,255,255,0.9) 0%, rgba(255,255,255,0.8) 100%);
   background: -webkit-linear-gradient(left,  rgba(255,255,255,0.9) 0%,rgba(255,255,255,0.8) 100%);
   background: linear-gradient(to right,  rgba(255,255,255,0.9) 0%,rgba(255,255,255,0.8) 100%); 
   filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#e6ffffff', endColorstr='#ccffffff',GradientType=1 );
   -webkit-border-radius: 10px;
   -moz-border-radius: 10px;
   border-radius: 10px;
   z-index: 99999;
}

@media (min-width: 2400px) {
	.corpo {
		zoom: 110%;
	}
}

@media (min-width: 2880px) {
	.corpo {
		zoom: 130%;
	}
}

@media (min-width: 3360px) {
	.corpo {
		zoom: 150%;
	}
}

@media (min-width: 3840px) {
	.corpo {
		zoom: 170%;
	}
}

.right_menu > .overflow-auto:not(.open) {
	display: none;
}

.right_menu > .right-sidebar:not(.open) {
	display: none;
}

.content {
   height: 100%;
   overflow: auto;
}

i:not(.normal) {
   font-size: 13px !important;
   transform: translateY(-0.5px);
}

.menu-app, .notif-menu, .task-menu {
   position: absolute !important;
   height: 97% !important;
}

body.menu_dark .theme-light-dark .t-dark {
   filter: invert(100%);
}

a {
   text-decoration: none !important;
}

.card {
   margin-bottom: 8px;
}

.row {
   margin: 0;
}

.row>*{
   padding-right: 4px;
   padding-left: 4px;
}

::-webkit-scrollbar {
	width: 8px;
	height: 8px;
}

::-webkit-scrollbar-track {
	background: #0000;
}

::-webkit-scrollbar-thumb {
	background: #aaa;
	border-radius: 0.25rem;
	transition: opacity 0.2s linear;
}

/* Animacao arregando */
.loadingContent {
	position: absolute;
   top: 0;
   left: 0;
	background-color: #fff;
	z-index: 99999;
	height: 100%;
	width: 100%;
   display: none;
}

.loading {
	width: 110px;
	height: 50px;
	position: relative;
	top: 50%;
	left: calc(50% - 55px);
}

.loading p {
	top: 0;
	padding: 0;
	margin: 0;
	color: #dd0f20;
	font-family: 'Oxygen', sans-serif;
	animation: text 3.5s ease both infinite;
	font-size: 12px;
	letter-spacing: 1px;
   font-weight: bold;
}

@keyframes text {
	0% {
		letter-spacing: 1px;
		transform: translateX(0px);
	}
	40% {
		letter-spacing: 2px;
		transform: translateX(26px);
	}
	80% {
		letter-spacing: 1px;
		transform: translateX(32px);
	}
	90% {
		letter-spacing: 2px;
		transform: translateX(0px);
	}
	100% {
		letter-spacing: 1px;
		transform: translateX(0px);
	}
}

.loading span {
	background-color: #ccc;
	border-radius: 50px;
	display: block;
	height: 16px;
	width: 16px;
	bottom: 0;
	position: absolute;
	transform: translateX(64px);
	animation: loading 3.5s ease both infinite;
}

.loading span:before {
	position: absolute;
	content: '';
	width: 100%;
	height: 100%;
	background-color: #dd0f20;
	border-radius: inherit;
	animation: loading2 3.5s ease both infinite;
}

@keyframes loading {
	0% {
		width: 16px;
		transform: translateX(0px);
	}
	40% {
		width: 100%;
		transform: translateX(0px);
	}
	80% {
		width: 16px;
		transform: translateX(64px);
	}
	90% {
		width: 100%;
		transform: translateX(0px);
	}
	100% {
		width: 16px;
		transform: translateX(0px);
	}
}
@keyframes loading2 {
	0% {
		transform: translateX(0px);
		width: 16px;
	}
	40% {
		transform: translateX(0%);
		width: 80%;
	}
	80% {
		width: 100%;
		transform: translateX(0px);
	}
	90% {
		width: 80%;
		transform: translateX(15px);
	}
	100% {
		transform: translateX(0px);
		width: 16px;
	}
}

.cards {
	gap: 0.5rem;
	padding: 5px;
	list-style-type: none;
	display: grid;
	grid-template-columns: repeat(auto-fill, minmax(130px, 1fr));
}

.cards-1 {
	gap: 0.5rem;
	padding: 5px;
	list-style-type: none;
	display: grid;
	grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
}

.cards-2 {
	gap: 0.5rem;
	padding: 5px;
	list-style-type: none;
	display: grid;
	grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
}

.cards img {
	width: 64px;
	height: 64px;
   object-fit: contain;
}

.form-label {
   font-size: 13px;
   margin-bottom: 4px !important;
}

.form-label i {
   font-size: 12px !important;
   transform: none;
}

input.form-control {
   padding: 11px 22px 10px !important;
}

button i {
   transform: none !important;
}

.font-16 {
	font-size: 16px !important;
}

.font-15 {
	font-size: 15px !important;
}

.font-14 {
	font-size: 14px !important;
}

.font-13 {
	font-size: 13px !important;
}

.font-12 {
	font-size: 12px !important;
}

.font-11 {
	font-size: 11px !important;
}

.font-10 {
	font-size: 10px !important;
}

.delimitarTexto {
   overflow: hidden;
   text-overflow: ellipsis;
	white-space: nowrap;
}

span.cursor-pointer:hover {
   text-decoration: underline;
}

.cursor-pointer {
   cursor: pointer;
}

.btn-icone {
   display: inline-block;
   cursor: pointer;
   text-align: center;
   overflow: hidden;
   text-overflow: ellipsis;
	white-space: nowrap;
}

.btn-icone small {
   display: none;
   font-size: 11px;
}

.btn-icone:hover small {
   display: block;
}

.modal {
   position: absolute !important;
}

.modal-content .modal-body {
   height: calc(80vh - 190px);
	max-height: 890px;
   overflow-y: auto;
}

.modal-footer button {
   display: flex;
   align-items: center;
}

.modal-footer button div {
   transition: opacity 0.5s ease-out;
   opacity: 0;
   height: 0;
   width: 0;
   overflow: hidden;
}

@media (min-width: 992px) {
   .modal-footer button:hover i.fa-plus {
      height: 0;
      width: 0;
      overflow: hidden;
   }
   
   .modal-footer button:hover div {
      opacity: 1;
      height: auto;
      width: auto;
      margin-right: 5px;
      margin-left: -8px;
   }
}

.right-0 {
   right: 0;
}

.swal2-input:focus,
.swal2-textarea:focus {
   box-shadow: none !important;
}

.swal2-textarea {
	resize: none !important;
}

#content-body {
   height: 68vh;
   overflow-y: auto;
   padding: 16px 24px;
}

.swal2-container {
   z-index: 999999 !important;
}

.weight-900 {
	font-weight: 900 !important;
}

.weight-800 {
	font-weight: 800 !important;
}

.weight-700 {
	font-weight: 700 !important;
}

.weight-600 {
	font-weight: 600 !important;
}

.weight-500 {
	font-weight: 500 !important;
}

.weight-400 {
	font-weight: 400 !important;
}

.weight-300 {
	font-weight: 300 !important;
}

.weight-200 {
	font-weight: 200 !important;
}

.weight-100 {
	font-weight: 100 !important;
}

.w-max-content {
	width: max-content !important;
}

.ui-autocomplete {
	z-index: 9999999 !important;
	max-height: 30vh;
	overflow-y: auto;
	overflow-x: hidden;
}

.btn-float {
	position: absolute;
	width: 55px;
	height: 55px;
	bottom: 15px;
	right: 15px;
	border-radius: 50px;
	text-align: center;
	box-shadow: 2px 2px 3px #999;
}

.btn-float i {
	margin-top: 20px;
}

#mensagens {
	overflow-y: auto;
	height: 80%;
}

#mensagens .card {
	max-width: 50%;
	width: max-content;
	padding: 12px 16px;
	border-radius: 24px;
	border-top-left-radius: 0px;
	box-shadow: 0 1px 2px 0 #eee;
	border: 1px solid #eee;
}

#mensagens .minhaMensagem {
	float: right;
	background-color: #D1D7E0 !important;
	color: #000;
	border-radius: 24px !important;
	border-top-right-radius: 0px !important;
}

#mensagens .nome {
	font-weight: 700;
	margin-bottom: 4px;
	overflow: hidden;
   display: -webkit-box;
   -webkit-line-clamp: 1;
   -webkit-box-orient: vertical;
	line-break: anywhere;
}

#mensagens .data {
	font-size: 10px;
	text-align: right;
	color: #777;
	overflow: hidden;
   display: -webkit-box;
   -webkit-line-clamp: 1;
   -webkit-box-orient: vertical;
	line-break: anywhere;
}

#mensagensChat {
	height: 20%;
}

#mensagensChat textarea {
	border: 1px solid #eee !important;
	height: 100% !important;
	width: 100%;
}
	
#mensagensChat textarea:focus {
	border-color: #ddd !important; 
}

.removeToHover {
	cursor: pointer;
}

.removeToHover h5:nth-child(2) {
	display: none;
}

.removeToHover h5:nth-child(2) i {
	font-size: 80%;
}

.removeToHover:hover > * {
	display: none;
}

.removeToHover:hover h5:nth-child(2) {
	display: block;
	margin: 0 !important;
	color: #FF3636;
}

textarea {
	border: 1px solid #E3E3E3 !important;
	border-radius: 5px !important;
	color: #2c2c2c !important;
	padding: 11px 22px 10px !important;
}

textarea:focus {
	border-color: #313740 !important;
}

.swal2-image {
	object-fit: contain !important;
}

.product__item {
	background-color: #fff;
	position: relative;
	padding: 15px 10px;
	padding-bottom: 30px;
	transition: all 0.3s ease-out 0s;
	overflow: hidden;
	border-radius: 5px;
	border: 1px solid #ddd;
}

.product__item:hover {
	border: 1px solid #ccc;
	box-shadow: 0 0 20px 0 rgb(155 166 200 / 20%);
}

.product__item:hover .product__add-btn {
	bottom: 15px;
	visibility: visible;
	opacity: 1;
}

.product__add-btn {
	position: absolute;
	bottom: -40px;
	right: 10px;
	left: 10px;
	margin: 0;
	text-align: center;
	visibility: hidden;
	opacity: 0;
	transition: all 0.2s ease-out 0s;
	display: flex;
	flex-wrap: wrap;
	background-color: #fff;
}

.product__offer {
	position: absolute;
	top: 7px;
	right: 15px;
}

.product__electronic-item .product__thumb .w-img img {
	position: absolute;
	top: 0;
	bottom: 0;
	left: 0;
	right: 0;
	width: 100%;
	height: 100%;
	-o-object-fit: cover;
	object-fit: cover;
	-o-object-position: center;
	object-position: center;
}

.w-img img {
	width: 100%;
	background-color: #fff;
	border-radius: 5px;
}

.product__electronic-item .product__thumb .w-img:after {
	content: '';
	display: block;
	padding-bottom: 100%;
}

.product__content {
	padding-top: 15px;
	text-align: center;
}

.product__content h6 {
	font-family: 'Rubik', sans-serif;
	margin-top: 0px;
	font-weight: 500;
	transition: all 0.3s ease-out 0s;
	padding-top: 4px;
	font-size: 14px;
	color: #222;
	line-height: 18px;
	margin-bottom: 8px;
	justify-content: center;
	overflow: hidden;
	display: -webkit-box;
	-webkit-line-clamp: 2;
	-webkit-box-orient: vertical;
	align-self: center;
}

.product__content span {
	font-size: 16px;
	font-weight: 500;
	color: #222;
	padding: 0 7px;
	display: inline-block;
}

.product__add-btn button {
	font-size: 13px;
	font-weight: 600;
	background-color: #fff;
	line-height: 43px;
	padding: 0;
	border: 1px solid #ddd;
	color: #dd0f20;
	border-radius: 8px;
	z-index: 1;
	text-transform: uppercase;
	width: 100%;
	margin: 0 auto;
}








