-----------------------------------------------------------------------------------------------------------------------------------------
-- ADMIN SYSTEM PREPARES
-----------------------------------------------------------------------------------------------------------------------------------------

-- Prepares para veículos
vRP.Prepare("vehicles/UserVehicles","SELECT * FROM vehicles WHERE Passport = @Passport")
vRP.Prepare("vehicles/addVehicles","INSERT INTO vehicles (Passport, vehicle, plate, work) VALUES (@Passport, @vehicle, @plate, @work)")
vRP.Prepare("vehicles/removeVehicles","DELETE FROM vehicles WHERE Passport = @Passport AND vehicle = @vehicle")
vRP.Prepare("vehicles/plateVehicles","SELECT * FROM vehicles WHERE plate = @plate")

-- Prepares para banidos
vRP.Prepare("banneds/GetBanned","SELECT * FROM banneds WHERE license = @license")
vRP.Prepare("banneds/InsertBanned","INSERT INTO banneds (license, time) VALUES (@license, @time)")
vRP.Prepare("banneds/RemoveBanned","DELETE FROM banneds WHERE license = @license")

-- Prepares para contas
vRP.Prepare("accounts/Account","SELECT * FROM accounts WHERE license = @license")

-- Prepares para dados de jogadores
vRP.Prepare("playerdata/GetData","SELECT * FROM playerdata WHERE Passport = @Passport AND dkey = @dkey")

-- Prepares para personagens
vRP.Prepare("characters/Person","SELECT * FROM characters WHERE id = @id")
vRP.Prepare("characters/addBank","UPDATE characters SET bank = bank + @amount WHERE id = @Passport")

-- Prepares para identidade falsa
vRP.Prepare("fidentity/Result","SELECT * FROM fidentity WHERE id = @id")
