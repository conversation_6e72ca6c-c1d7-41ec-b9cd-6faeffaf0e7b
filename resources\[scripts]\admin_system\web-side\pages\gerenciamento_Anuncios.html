<div id="local">
   <div class="row">
      <div class="col-6">
         <div class="card">
            <div class="body">
               <div class="col-12">
                  <h4 class="mt-0 mb-3">Enviar</h4>
               </div>
               <div class="col-12 mb-2">
                  <label class="form-label"><i class="far fa-stream text-primary me-1"></i> Tipo</label>
                  <select class="form-control" v-model="anuncio.tipo">
                     <option>Identidade</option>
                     <option>Permissão</option>
                  </select>
               </div>
               <div class="col-12 mb-2" :class="anuncio.tipo == 'Identidade' ? '' : 'd-none'">
                  <label class="form-label"><i class="far fa-hashtag text-primary me-1"></i> Identidade</label>
                  <input type="text" class="form-control" v-model="anuncio.id" />
               </div>
               <div class="col-12 mb-2" :class="anuncio.tipo == 'Permissão' ? '' : 'd-none'">
                  <label class="form-label"><i class="far fa-lock text-primary me-1"></i> Permissão</label>
                  <select class="form-control shadow-none" v-model="anuncio.permissao">
                     <option value="Todos">Todos</option>
                     <option v-for="(permissao, index) in global.permissoes" :key="index" :value="permissao">{{ permissao }}</option>
                  </select>
               </div>
               <div class="col-12 mb-2">
                  <label class="form-label"><i class="fas fa-ellipsis-h text-primary me-1"></i> Mensagem</label>
                  <textarea class="form-control" v-model="anuncio.mensagem" maxlength="100"></textarea>
               </div>
               <div class="col-12 text-end">
                  <button class="btn btn-primary" @click="configAd">
                     <i class="far fa-check me-2"></i>Enviar
                  </button>
               </div>
            </div>
         </div>
      </div>
      <div class="col-6">
         <div class="card">
            <div class="body">
               <h4 class="mt-0 mb-3">Meus anúncios</h4>
               
               <div class="row">
                  <div class="body py-2 px-3 mb-2 font-13 cursor-pointer" v-for="(ad, index) in meusAnuncios" :key="index" @click="sendAd(ad)">
                     {{ ad.message }}
                  </div>
               </div>
               <div v-if="meusAnuncios == null || meusAnuncios.length == 0">Nenhum anúncio encontrado.</div>
            </div>
         </div>
      </div>
   </div>
</div>

<script>
   var local = new Vue({
      el: '#local',
      data: {
         meusAnuncios: localStorage.myAds != undefined ? JSON.parse(localStorage.myAds) : [],
         anuncio: {'tipo': 'Permissão', 'id': '', 'permissao': 'Todos', 'mensagem': ''},
      }
   })

   getPermissions().then((data) => {
      setTimeout(() => {
         $('select').selectpicker();
      }, 300);
   })

   function configAd() {
      let body = {'type': local.anuncio.tipo, 'value': null, 'message': local.anuncio.mensagem}

      switch (local.anuncio.tipo) {
         case "Identidade":
            body.value = String(local.anuncio.id).trim()
         break;
         case "Permissão":
            body.value = String(local.anuncio.permissao).trim()
         break;
      }

      sendAd(body).then((data) => {
         local.meusAnuncios.unshift(body)
         localStorage.myAds = JSON.stringify(local.meusAnuncios)

         local.anuncio.id = '';
         local.anuncio.permissao = 'Todos';
         local.anuncio.mensagem = '';
      })
   }
</script>