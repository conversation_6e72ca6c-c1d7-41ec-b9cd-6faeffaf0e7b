
<div id="local">
   <div class="card">
      <div class="body p-3">
         <div class="row">
            <div class="col-md-5 col-6">
               <label class="form-label"><i class="far fa-stream text-primary me-1"></i> Tipo de pesquisa</label>
               <select class="form-control" v-model="pesquisa.tipo">
                  <option>Identidade</option>
                  <option>Steam</option>
                  <option>Permissão</option>
                  <option>Whitelist</option>
                  <option>Online</option>
                  <!-- <option>Código de aprovação</option> -->
               </select>
            </div>
            <div class="col-md-5 col-6" :class="pesquisa.tipo == 'Identidade' ? '' : 'd-none'">
               <label class="form-label"><i class="far fa-hashtag text-primary me-1"></i> Identidade</label>
               <input type="text" class="form-control" v-model="pesquisa.id" @keyup.enter="configUsers" />
            </div>
            <div class="col-md-5 col-6" :class="pesquisa.tipo == 'Steam' ? '' : 'd-none'">
               <label class="form-label"><i class="fab fa-steam text-primary me-1"></i> Steam</label>
               <input type="text" class="form-control" v-model="pesquisa.steam" @keyup.enter="configUsers" />
            </div>
            <div class="col-md-5 col-6" :class="pesquisa.tipo == 'Permissão' ? '' : 'd-none'">
               <label class="form-label"><i class="far fa-lock text-primary me-1"></i> Permissão</label>
               <select class="form-control shadow-none" v-model="pesquisa.permissao">
                  <option v-for="(permissao, index) in global.permissoes" :key="index" :value="permissao">{{ permissao }}</option>
               </select>
            </div>
            <div class="col-md-5 col-6" :class="pesquisa.tipo == 'Whitelist' ? '' : 'd-none'">
               <label class="form-label"><i class="far fa-scroll text-primary me-1"></i> Whitelist</label>
               <select class="form-control shadow-none" v-model="pesquisa.whitelist">
                  <option>Não aprovado</option>
                  <option>Aprovado</option>
               </select>
            </div>
            <div class="col-md-5 col-6" :class="pesquisa.tipo == 'Online' ? '' : 'd-none'">
               <label class="form-label"><i class="far fa-wifi text-primary me-1"></i> Online</label>
               <select class="form-control shadow-none" v-model="pesquisa.online">
                  <option>Jogador</option>
                  <option>Policia</option>
                  <option>Hospital</option>
                  <option>Staff</option>
               </select>
            </div>
            <div class="col-md-5 col-6" :class="pesquisa.tipo == 'Código de aprovação' ? '' : 'd-none'">
               <label class="form-label"><i class="far fa-hashtag text-primary me-1"></i> Código de aprovação</label>
               <input type="text" class="form-control" v-model="pesquisa.codigoAprovacao" @keyup.enter="configUsers" />
            </div>
            <div class="col-md-2 align-self-end mt-md-0 mt-2">
               <button class="btn btn-primary w-100 m-0" @click="configUsers">
                  <i class="fas fa-search"></i>
               </button>
            </div>
         </div>
      </div>
   </div>

   <!-- Modal detalhes jogador -->
   <div class="modal" id="modalInfoUser" role="dialog" v-if="global.permissao.NV1 || global.permissao.NV2">
      <div class="modal-dialog modal-lg" role="document">
         <div class="modal-content">
            <div class="modal-header">
               <h4 class="title" id="modalInfoUserLabel">Detalhes do jogador</h4>
            </div>
            <div class="modal-body py-3">
               <div class="card" v-if="!add.item && !add.veiculo && !add.permissao">
                  <div class="body p-3 mx-1 mb-1"> 
                     <ul class="nav nav-tabs p-0" role="tablist">
                        <li class="nav-item">
                           <a class="nav-link rounded active" data-toggle="tab" href="#tabOne">
                              <i class="fal fa-backpack normal me-1"></i> Itens
                           </a>
                        </li>
                        <li class="nav-item">
                           <a class="nav-link rounded" data-toggle="tab" href="#tabTwo">
                              <i class="fal fa-car-side normal me-1"></i> Veículos
                           </a>
                        </li>
                        <li class="nav-item">
                           <a class="nav-link rounded" data-toggle="tab" href="#tabThree">
                              <i class="fal fa-lock normal me-1"></i> Permissões
                           </a>
                        </li>
                     </ul>
                  </div>
                     
                  <div class="tab-content">
                     <div role="tabpanel" class="tab-pane in active" id="tabOne">
                        <div class="cards">
                           <div class="body text-center p-2 position-relative" v-for="(i, index) in infoUser.itens" :key="index">
                              <i v-if="global.permissao.NV1" class="far fa-trash position-absolute top-0 right-0 cursor-pointer m-2 normal font-15 text-danger" @click="configRemoveItem(i.item, index)"></i>
                              <img class="mb-2" :src="global.urlFotoItens + i.item +'.png'" @error="imageError" /><br>
                              <h6 class="m-0 delimitarTexto">{{ i.item }}</h6>
                              <small>{{ i.amount }} <small>UN</small></small>
                           </div>
                        </div>

                        <div v-if="infoUser.itens == null || infoUser.itens.length == 0">Nenhum item encontrado.</div>
                     </div>
                     <div role="tabpanel" class="tab-pane" id="tabTwo">
                        <div class="cards">
                           <div class="body text-center p-2 position-relative" v-for="(v, index) in infoUser.veiculos" :key="index">
                              <i v-if="global.permissao.NV1" class="far fa-trash position-absolute top-0 right-0 cursor-pointer m-2 normal font-15 text-danger" @click="configRemoveVehicle(v.vehicle, index)"></i>
                              <img class="mb-2" :src="global.urlFotoVeiculos + v.vehicle +'.png'" @error="imageError" /><br>
                              <h6 class="m-0 delimitarTexto">{{ v.vehicle }}</h6>
                              <small><i class="far fa-car-side text-primary me-1"></i>{{ v.body }}<small>%</small></small>
                              <small class="ms-2"><i class="far fa-gas-pump text-primary me-1"></i>{{ v.fuel }}<small>%</small></small><br>
                              <small><i class="far fa-car-battery text-primary me-1"></i>{{ parseInt(v.engine) / 10 }}<small>%</small></small>
                           </div>
                        </div>

                        <div v-if="infoUser.veiculos == null || infoUser.veiculos.length == 0">Nenhum veículo encontrado.</div>
                     </div>
                     <div role="tabpanel" class="tab-pane" id="tabThree">
                        <div class="cards-2">
                           <div class="body text-center" v-for="(perm, index) in infoUser.permissoes" :key="index">
                              <i class="far fa-trash float-end cursor-pointer normal font-15 text-danger ms-2" @click="configRemovePerm(perm, index)"></i>
                              <h6 class="m-0 delimitarTexto">{{ perm }}</h6>
                           </div>
                        </div>
                     </div>
                  </div>
               </div>
               
               <div class="card" v-if="add.item">
                  <div class="body p-3 pt-2 mx-1 mb-1">
                     <label class="form-label"><i class="far fa-backpack text-primary me-1"></i> Item</label>
                     <input type="text" class="form-control searchInput" v-model="add.searchItem" placeholder="Pesquisar..." />
                  </div>

                  <div id="searchItem" class="cards">
                     <div class="body text-center p-2 cursor-pointer delimitarTexto weight-700 font-14" v-for="(item, index) in global.itens" :key="index" @click="configAddItem(item)">
                        <img class="lazyload mb-2" :src="global.urlFotoItens + item +'.png'" @error="imageError" /><br>
                        {{ item }}
                     </div>
                  </div>

                  <div v-if="global.itens == null || global.itens.length == 0">Nenhum item encontrado.</div>
               </div>
               
               <div class="card" v-if="add.veiculo">
                  <div class="body p-3 pt-2 mx-1 mb-1">
                     <label class="form-label"><i class="far fa-car-side text-primary me-1"></i> Veículo</label>
                     <input type="text" class="form-control searchInput" v-model="add.searchVehicle" placeholder="Pesquisar..." />
                  </div>

                  <div id="searchVehicle" class="cards">
                     <div class="body text-center p-2 cursor-pointer delimitarTexto weight-700 font-14" v-for="(veiculo, index) in global.veiculos" :key="index" @click="configAddVehicle(veiculo)">
                        <img class="lazyload mb-2" :src="global.urlFotoVeiculos + veiculo +'.png'" @error="imageError" /><br>
                        {{ veiculo }}
                     </div>
                  </div>

                  <div v-if="global.veiculos == null || global.veiculos.length == 0">Nenhum veiculo encontrado.</div>
               </div>
               
               <div class="card" v-if="add.permissao">
                  <div class="body p-3 pt-2 mx-1 mb-1">
                     <label class="form-label w-100">
                        <i class="far fa-lock text-primary me-1"></i> Permissão
                        <span class="text-primary float-end">Pressione 'enter' para adicionar</span>
                     </label>
                     <input type="text" class="form-control searchInput" v-model="add.searchPerm" placeholder="Pesquisar ou enter para adicionar..." @keyup.enter="configAddPerm(add.searchPerm)" />
                  </div>

                  <div id="searchPerm" class="cards">
                     <div class="body text-center p-2 cursor-pointer delimitarTexto weight-700 font-14" v-for="(permissao, index) in global.permissoes" :key="index" @click="configAddPerm(permissao)">
                        {{ permissao }}
                     </div>
                  </div>

                  <div v-if="global.permissoes == null || global.permissoes.length == 0">Nenhuma permissão encontrada.</div>
               </div>
            </div>
            <div class="modal-footer">
               <button type="button" class="btn btn-default waves-effect me-2" @click="toggleAdd('ITEM')" v-if="!add.item && !add.veiculo && !add.permissao && global.permissao.NV1">
                  <i class="far fa-plus me-2"></i><div>Add</div>Item
               </button>
               <button type="button" class="btn btn-default waves-effect me-2" @click="toggleAdd('VEICULO')" v-if="!add.item && !add.veiculo && !add.permissao && global.permissao.NV1">
                  <i class="far fa-plus me-2"></i><div>Add</div>Veículo
               </button>
               <button type="button" class="btn btn-default waves-effect me-2" @click="toggleAdd('PERMISSAO')" v-if="!add.item && !add.veiculo && !add.permissao && global.permissao.NV1">
                  <i class="far fa-plus me-2"></i><div>Add</div>Permissão
               </button>
               <button type="button" class="btn btn-default waves-effect me-2" @click="toggleAdd('RESET')" v-if="add.item || add.veiculo || add.permissao">
                  <i class="far fa-undo me-2"></i>Voltar
               </button>
               <button type="button" class="btn btn-danger btn-simple waves-effect" data-dismiss="modal">Fechar</button>
            </div>
         </div>
      </div>
   </div>

   <!-- Lista de jogadores -->
   <div class="row">
      <div class="col-12 px-0" v-for="(u, index) in resultado" :key="index">
         <div class="card">
            <div class="body px-3 py-10">
               <div class="row">
                  <div class="col-md-7 col-lg-8 col-xl-9">
                     <h5 class="delimitarTexto font-17 mb-2">
                        {{ u.name != null && u.name2 != null ? u.name +" "+ u.name2 : 'Desconhecido' }}
                     </h5>
                     <div class="row text-muted font-12 mb-1"> 
                        <div class="col delimitarTexto">
                           <i class="far fa-hashtag text-primary font-10 normal me-1 d-none d-xl-inline"></i>
                           <small>Identidade:</small> {{ u.id != null ? u.id : '-' }} 
                        </div>
                        <div class="col delimitarTexto">
                           <i class="fab fa-steam text-primary font-10 normal me-1 d-none d-xl-inline"></i>
                           <small>Steam:</small> {{ u.steam != null ? String(u.steam).replace('steam:', '') : '-' }} 
                        </div>
                        <div class="col delimitarTexto">
                           <span v-if="global.permissao.NV1" @click="configMoney(u)" class="cursor-pointer">
                              <i class="far fa-money-bill-alt text-primary font-10 normal me-1 d-none d-xl-inline"></i>
                              <small>Banco:</small> {{ u.bank != null ? u.bank : '-' }}
                           </span>
                           <span v-else>
                              <i class="far fa-money-bill-alt text-primary font-10 normal me-1 d-none d-xl-inline"></i>
                              <small>Banco:</small> {{ u.bank != null ? u.bank : '-' }}
                           </span>
                        </div>
                        <div class="col delimitarTexto">
                           <span v-if="global.permissao.NV1" @click="configCoins(u)" class="cursor-pointer">
                              <i class="far fa-coins text-primary font-10 normal me-1 d-none d-xl-inline"></i>
                              <small>Coins:</small> {{ u.gems != null ? u.gems : '-' }}
                           </span>
                           <span v-else>
                              <i class="far fa-coins text-primary font-10 normal me-1 d-none d-xl-inline"></i>
                              <small>Coins:</small> {{ u.gems != null ? u.gems : '-' }}
                           </span>
                        </div>
                     </div>
                     <div class="row text-muted font-12">
                        <!-- <div class="col delimitarTexto">
                           <span v-if="global.permissao.NV1" @click="configCharacters(u)" class="cursor-pointer">
                              <i class="far fa-users text-primary font-10 normal me-1 d-none d-xl-inline"></i>
                              <small>Personagens:</small> {{ u.chars != null ? u.chars : '-'  }} 
                           </span>
                           <span v-else>
                              <i class="far fa-users text-primary font-10 normal me-1 d-none d-xl-inline"></i>
                              <small>Personagens:</small> {{ u.chars != null ? u.chars : '-'  }} 
                           </span>
                        </div> -->
                        <!-- <div class="col delimitarTexto">
                           <span v-if="global.permissao.NV1" @click="configParkingSpaces(u)" class="cursor-pointer">
                              <i class="far fa-warehouse text-primary font-10 normal me-1 d-none d-xl-inline"></i>
                              <small>Garagem:</small> {{ u.garage != null ? u.garage : '-' }} 
                           </span>
                           <span v-else>
                              <i class="far fa-warehouse text-primary font-10 normal me-1 d-none d-xl-inline"></i>
                              <small>Garagem:</small> {{ u.garage != null ? u.garage : '-' }} 
                           </span>
                        </div> -->
                        <div class="col delimitarTexto">
                           <span @click="configWhitelist(u)" class="cursor-pointer text-success" v-if="u.whitelist == true">
                              <i class="far fa-scroll font-10 normal me-1 d-none d-xl-inline"></i>
                              <small>Whitelist:</small> Sim
                           </span>
                           <span @click="configWhitelist(u)" class="cursor-pointer text-danger" v-else>
                              <i class="far fa-scroll font-10 normal me-1 d-none d-xl-inline"></i>
                              <small>Whitelist:</small> {{ u.whitelist != null ? 'Não' : '-' }}
                           </span>
                        </div>
                        <div class="col delimitarTexto">
                           <span @click="configBan(u)" class="cursor-pointer text-success" v-if="u.banned == true">
                              <i class="far fa-ban font-10 normal me-1 d-none d-xl-inline"></i>
                              <small>Banido:</small> Sim
                           </span>
                           <span @click="configBan(u)" class="cursor-pointer text-danger" v-else>
                              <i class="far fa-ban font-10 normal me-1 d-none d-xl-inline"></i>
                              <small>Banido:</small> {{ u.banned != null ? 'Não' : '-' }}
                           </span>
                        </div>
                     </div>
                  </div>
                  <div class="col-md-5 col-lg-4 col-xl-3 d-inline-flex align-items-center justify-content-center mt-md-0 mt-3">
                     <a v-if="global.permissao.NV1 || global.permissao.NV2" class="col btn-icone text-dark" @click="configInfoUser(u.id)" href="#modalInfoUser" data-toggle="modal" data-target="#modalInfoUser">
                        <i class="far fa-info-circle font-17 normal"></i><small>Detalhes</small>
                     </a>
                     <a v-if="global.permissao.NV1 || global.permissao.NV2 || global.permissao.NV3" class="col btn-icone text-dark" @click="tptoUser(u.id)" href="javascript:;">
                        <i class="far fa-portal-enter font-16 normal"></i><small>Teleportar</small>
                     </a>
                     <a v-if="global.permissao.NV1 || global.permissao.NV2 || global.permissao.NV3" class="col btn-icone text-dark" @click="goodUser(u.id)" href="javascript:;">
                        <i class="far fa-heart font-16 normal"></i><small>Regenerar</small>
                     </a>
                  </div>
               </div>
            </div>
         </div>
      </div>
   </div>
   <div v-if="resultado == null || resultado.length == 0">Nenhum jogador encontrado.</div>
</div>

<script>
   var local = new Vue({
      el: '#local',
      data: {
         add: {'item': false, 'veiculo': false, 'permissao': false, 'searchItem': '', 'searchVehicle': '', 'searchPerm': ''},
         infoUser: {"id": 0, "itens": [], "permissoes": [], "veiculos": []},
         pesquisa: {'tipo': 'Online', 'id': '', 'steam': '', 'permissao': null, 'whitelist': 'Não aprovado', 'online': 'Jogador', 'codigoAprovacao': ''},
         resultado: []
      },
      watch: {
         'add.searchItem' : function (val) {
            var value = val.toLowerCase()

            $("#searchItem .body").filter(function () {
               $(this).toggle($(this).text().toLowerCase().indexOf(value) > -1)
            })
         },
         'add.searchVehicle' : function (val) {
            var value = val.toLowerCase()

            $("#searchVehicle .body").filter(function () {
               $(this).toggle($(this).text().toLowerCase().indexOf(value) > -1)
            })
         },
         'add.searchPerm' : function (val) {
            var value = val.toLowerCase()

            $("#searchPerm .body").filter(function () {
               $(this).toggle($(this).text().toLowerCase().indexOf(value) > -1)
            })
         }
      },
   })

   configUsers()

   getPermissions().then((data) => {
      if (global.permissoes[0] != null) {
         local.pesquisa.permissao = global.permissoes[0]

         setTimeout(() => {
            $('select').selectpicker();
         }, 300);
      }
   })

   getItems().then((data) => {
      lazyload()
   })

   getVehicles().then((data) => {
      lazyload()
   })

   function toggleAdd(tipo) {
      toggleLoading(true)

      setTimeout(() => {
         if (tipo == 'ITEM') {
            local.add.item = true;

         } else if (tipo == 'VEICULO') {
            local.add.veiculo = true;

         } else if (tipo == 'PERMISSAO') {
            local.add.permissao = true;

         } else {
            local.add = {'item': false, 'veiculo': false, 'permissao': false, 'searchItem': '', 'searchVehicle': '', 'searchPerm': ''}
         }
      }, 300);
      
      setTimeout(() => {
         $(".searchInput").focus()
      }, 600);

      setTimeout(() => {
         toggleLoading(false)
      }, 900);
   }

   function configInfoUser(id) {
      getInfoUser(id).then((data) => {
         local.infoUser = data
      })
   }
   
   function configAddVehicle(vehicle) {
      setVehicleUser(local.infoUser.id, vehicle).then((data) => {
         local.infoUser.veiculos.push({
            "body": 100,
            "engine": 1000,
            "fuel": 100,
            "vehicle": vehicle
         })
      })
   }
   
   function configAddItem(item) {
      setItemUser(local.infoUser.id, item).then((data) => {
         local.infoUser.itens.push({
            "amount": data,
            "item": item
         })
      })
   }
   
   function configAddPerm(perm) {
      setPermissionUser(local.infoUser.id, perm).then((data) => {
         local.infoUser.permissoes.push(perm)
      })
   }

   function configRemoveItem(item, index) {
      removeItemUser(local.infoUser.id, item).then((data) => {
         let obj = local.infoUser.itens[index]

         if (data >= obj.amount) {
            local.infoUser.itens.splice(index, 1)
         } else {
            local.infoUser.itens[index].amount -= data
         }
      })
   }
   
   function configRemoveVehicle(vehicle, index) {
      removeVehicleUser(local.infoUser.id, vehicle).then((data) => {
         local.infoUser.veiculos.splice(index, 1)
      })
   }
   
   function configRemovePerm(perm, index) {
      removePermissionUser(local.infoUser.id, perm).then((data) => {
         local.infoUser.permissoes.splice(index, 1)
      })
   }
   
   function configMoney(usuario) {
      changeMoney(usuario.id).then((data) => {
         usuario.bank = data
      })
   }

   function configCoins(usuario) {
      changeCoins(usuario.id).then((data) => {
         usuario.gems = data
      })
   }

   function configCharacters(usuario) {
      changeCharacters(usuario.id).then((data) => {
         usuario.chars = data
      })
   }

   function configCharacters(usuario) {
      changeCharacters(usuario.id).then((data) => {
         usuario.chars = data
      })
   }

   function configParkingSpaces(usuario) {
      changeParkingSpaces(usuario.id).then((data) => {
         usuario.garage = data
      })
   }

   function configWhitelist(usuario) {
      toggleWhitelist(usuario.steam, usuario.whitelist == true).then((data) => {
         usuario.whitelist = !usuario.whitelist
      })
   }

   function configBan(usuario) {
      toggleBan(usuario.id, usuario.banned == true).then((data) => {
         usuario.banned = !usuario.banned
      })
   }

   function configUsers() {
      let body = {'type': local.pesquisa.tipo, 'value': null}

      switch (local.pesquisa.tipo) {
         case "Identidade":
            body.value = String(local.pesquisa.id).trim()
         break;
         case "Steam":
            body.value = String(local.pesquisa.steam).trim()
         break;
         case "Permissão":
            body.value = String(local.pesquisa.permissao).trim()
         break;
         case "Whitelist":
            body.value = String(local.pesquisa.whitelist).trim()
         break;
         case "Online":
            body.value = String(local.pesquisa.online).trim()
         break;
         case "Código de aprovação":
            body.value = String(local.pesquisa.codigoAprovacao).trim()
         break;
      }

      searchUsers(body).then((data) => {
         local.resultado = data
      })
   }

</script>