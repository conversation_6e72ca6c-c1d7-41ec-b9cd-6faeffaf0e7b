-- monkey.monkey_system_tickets definition

CREATE TABLE `monkey_system_tickets` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `user_id` bigint(20) NOT NULL,
  `date` datetime NOT NULL,
  `status` varchar(100) NOT NULL DEFAULT 'Aberto',
  `rating` varchar(100) DEFAULT NULL,
  `title` varchar(100) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4;

-- monkey.monkey_system_ticket_messages definition

CREATE TABLE `monkey_system_ticket_messages` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `user_id` bigint(20) NOT NULL,
  `name` varchar(100) NOT NULL,
  `message` varchar(500) NOT NULL,
  `date` datetime NOT NULL,
  `idTicket` bigint(20) NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4;

-- monkey.monkey_system_adv definition

CREATE TABLE `monkey_system_adv` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `user_id` bigint(20) NOT NULL,
  `description` varchar(500) NOT NULL,
  `validity` int(11) NOT NULL,
  `date` datetime NOT NULL,
  `photo` varchar(255) DEFAULT NULL,
  `deleted` tinyint(1) NOT NULL DEFAULT 0,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4;

-- monkey.monkey_system_organization definition

CREATE TABLE `monkey_system_organization` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `blocked` bit(1) DEFAULT b'0',
  `dateCreation` datetime DEFAULT NULL,
  `idOwner` bigint(20) DEFAULT NULL,
  `maxMembers` int(11) DEFAULT NULL,
  `name` varchar(255) DEFAULT NULL,
  `onlineMembers` int(11) DEFAULT 0,
  `urlPhoto` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4;


-- monkey.monkey_system_organization_blacklist definition

CREATE TABLE `monkey_system_organization_blacklist` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` bigint(20) NOT NULL,
  `date` date NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4;

-- monkey.monkey_system_salary definition

CREATE TABLE `monkey_system_salary` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `permission` varchar(255) DEFAULT NULL,
  `salary` int(11) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4;

-- monkey.monkey_system_org_permissions definition

CREATE TABLE `monkey_system_org_permissions` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `idOrganization` bigint(20) DEFAULT NULL,
  `limit` int(11) DEFAULT NULL,
  `name` varchar(255) DEFAULT NULL,
  `permission` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4;

-- monkey.monkey_system_purchases_history definition

CREATE TABLE `monkey_system_purchases_history` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `user_id` bigint(20) NOT NULL,
  `item` varchar(100) NOT NULL,
  `category` varchar(100) NOT NULL,
  `date` datetime NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4;

-- monkey.monkey_system_banned_players definition

CREATE TABLE `monkey_system_banned_players` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `steam` varchar(100) NOT NULL,
  `validity` int(11) NOT NULL,
  `date` datetime NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4;

-- monkey.monkey_system_organization_blacklist definition

CREATE TABLE `monkey_system_organization_blacklist` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` bigint(20) NOT NULL,
  `date` date NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4;