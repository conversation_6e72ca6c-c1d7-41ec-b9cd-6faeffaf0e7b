Config = {}

Config.login = "CidadeDosSonhos"
Config.senha = "CidadeDosSonhos123"

-- Key to open the system
Config.keyTablet = 83
Config.commandTablet = "openpanel"

Config.notifyEvent = "Notify"

-- Module with function revivePlayer
Config.survivalModule = "survival"

Config.logo = "https://cdn.discordapp.com/attachments/943970427804475402/1025087507978801222/logo_monkey_sem_fundo.png"

Config.urlImgLoja = "https://j4v4.site/MC/carrosVip/"
Config.urlImgItens = "https://j4v4.site/MC/itens/"
Config.urlImgCarros = "https://j4v4.site/MC/carros/"

-- Car category that will show in the store
Config.carsStore = "donate"

-- Time to pay salary
Config.salaryMinutes = 60

-- Ticket status
Config.closedTicketStatus = "Fechado"

Config.notEnoughMoney = 'Você não tem dinheiro suficiente para esta compra!'
Config.notEnoughCoin = 'Você não tem coin suficiente para esta compra!'
Config.salaryReceived = 'Voce recebeu seu salario.'

-- VIP name
Config.vips = {"premium01","premium02","premium03","premium04"}

-- Permissions
Config.adminPerm = "Admin"
Config.ownerPerm = "Owner"
Config.policePerm = "Police"
Config.paramedicPerm = "Paramedic"

-- VIP itens
Config.loja = {
    ["vips"] = {
        ["Bronze"] = {
            ["item"] = "premium01",
            ["description"] = "Vip bronze",
            ["value"] = 30,
            ["quantity"] = 1 
        },
        ["Prata"] = {
            ["item"] = "premium02",
            ["description"] = "Vip prata",
            ["value"] = 50,
            ["quantity"] = 1 
        },
        ["Ouro"] = {
            ["item"] = "premium03",
            ["description"] = "Vip ouro",
            ["value"] = 100,
            ["quantity"] = 1 
        },
        ["Platina"] = {
            ["item"] = "premium04",
            ["description"] = "Vip platina",
            ["value"] = 180,
            ["quantity"] = 1 
        },
    }
}

-- WEBHOOK LOJA --
Config.webhook_veiculos = "Sua web hook aqui"
Config.webhook_desban = "Sua web hook aqui"
Config.webhook_permissao = "Sua web hook aqui"
Config.webhook_money = "Sua web hook aqui"
Config.webhook_coins = "Sua web hook aqui"
Config.webhook_garage = "Sua web hook aqui"
Config.webhook_item = "Sua web hook aqui"


Config.diasIPTU = 15
Config.diasRemoverHome = 3
Config.taxaIPTU = 10 

Config.upgrades = {
    ['pacote1'] = {
        ['casas'] = {
            [1] = { ['i'] = "n1t1_1", ['v'] = 200000 },
            [2] = { ['i'] = "n1t1_2", ['v'] = 250000 },
            [3] = { ['i'] = "n1t1_3", ['v'] = 300000 },
            [4] = { ['i'] = "n1t2_1", ['v'] = 200000 },
            [5] = { ['i'] = "n1t2_2", ['v'] = 250000 },
            [6] = { ['i'] = "n1t2_3", ['v'] = 300000 },
            [7] = { ['i'] = "n1t3_1", ['v'] = 200000 },
            [8] = { ['i'] = "n1t3_2", ['v'] = 250000 },
            [9] = { ['i'] = "n1t3_3", ['v'] = 300000 },
        },
        ['init_bau'] = 25,
        ['init_chaves'] = 1,
        ['multiplicador'] = 2,
        ['valor_upgrade_init_bau'] = 25000,
        ['valor_upgrade_init_chave'] = 25000
    }
}

return Config

-- Remove expired permissions
-- Citizen.CreateThread(function()
--     while true do
--         local rows = vRP.query("monkeySystem/getExpiredPermissions")
--         for k,v in pairs(rows) do
--             vRP.execute("monkeySystem/remPermissionbyId", { id = v.id })
--         end
--     end
-- end)