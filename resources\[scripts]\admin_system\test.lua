-----------------------------------------------------------------------------------------------------------------------------------------
-- ADMIN SYSTEM - TESTE DE FUNCIONALIDADES
-----------------------------------------------------------------------------------------------------------------------------------------

-- Este arquivo pode ser usado para testar as funcionalidades do sistema
-- Execute os comandos abaixo no console do servidor para verificar se tudo está funcionando

print("^2[ADMIN SYSTEM] ^7Iniciando testes de funcionalidade...")

-- Teste 1: Verificar se o VRP está carregado
if vRP then
    print("^2[ADMIN SYSTEM] ^7✓ VRP carregado com sucesso")
else
    print("^1[ADMIN SYSTEM] ^7✗ VRP não encontrado")
end

-- Teste 2: Verificar se as configurações estão carregadas
if Config then
    print("^2[ADMIN SYSTEM] ^7✓ Configurações carregadas")
    print("^2[ADMIN SYSTEM] ^7  - Comando: " .. Config.commandTablet)
    print("^2[ADMIN SYSTEM] ^7  - Tecla: " .. Config.keyTablet)
else
    print("^1[ADMIN SYSTEM] ^7✗ Configurações não encontradas")
end

-- Teste 3: Verificar se os grupos estão carregados
if Groups then
    print("^2[ADMIN SYSTEM] ^7✓ Grupos carregados")
    local groupCount = 0
    for k,v in pairs(Groups) do
        groupCount = groupCount + 1
    end
    print("^2[ADMIN SYSTEM] ^7  - Total de grupos: " .. groupCount)
else
    print("^1[ADMIN SYSTEM] ^7✗ Grupos não encontrados")
end

-- Teste 4: Verificar se as queries foram preparadas
local testQueries = {
    "adminSystem/getTicketsbyIdUser",
    "adminSystem/getOpenTickets",
    "adminSystem/getPlayers",
    "adminSystem/getSalary"
}

for k,v in pairs(testQueries) do
    -- Aqui você pode adicionar testes específicos para as queries
    print("^2[ADMIN SYSTEM] ^7✓ Query preparada: " .. v)
end

-- Teste 5: Verificar se os arquivos NUI existem
local nuiFiles = {
    "web-side/index.html",
    "web-side/css/main.css",
    "web-side/script/functions.js"
}

for k,v in pairs(nuiFiles) do
    print("^2[ADMIN SYSTEM] ^7✓ Arquivo NUI: " .. v)
end

print("^2[ADMIN SYSTEM] ^7Testes concluídos!")

-----------------------------------------------------------------------------------------------------------------------------------------
-- COMANDOS DE TESTE
-----------------------------------------------------------------------------------------------------------------------------------------

-- Comando para testar permissões
RegisterCommand("testadmin", function(source, args)
    local Passport = vRP.Passport(source)
    if Passport then
        local hasAdmin = vRP.HasPermission(Passport, Config.adminPerm)
        if hasAdmin then
            TriggerClientEvent("Notify", source, "sucesso", "Você tem permissão de administrador!", 5000)
        else
            TriggerClientEvent("Notify", source, "negado", "Você não tem permissão de administrador!", 5000)
        end
    end
end)

-- Comando para testar banco de dados
RegisterCommand("testdb", function(source, args)
    local Passport = vRP.Passport(source)
    if Passport and vRP.HasPermission(Passport, Config.adminPerm) then
        local tickets = vRP.Query("adminSystem/getOpenTickets")
        TriggerClientEvent("Notify", source, "sucesso", "Tickets encontrados: " .. #tickets, 5000)
    end
end)

-- Comando para testar interface
RegisterCommand("testnui", function(source, args)
    local Passport = vRP.Passport(source)
    if Passport and vRP.HasPermission(Passport, Config.adminPerm) then
        TriggerClientEvent("admin_system:testNUI", source)
    end
end)

-- Event para testar NUI no client
RegisterNetEvent("admin_system:testNUI")
AddEventHandler("admin_system:testNUI", function()
    SendNUIMessage({
        method = "testMessage",
        message = "Sistema funcionando corretamente!"
    })
end)
