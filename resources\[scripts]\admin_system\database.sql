-- ========================================
-- ADMIN SYSTEM DATABASE TABLES
-- ========================================

-- Tabela de tickets do sistema
CREATE TABLE IF NOT EXISTS `admin_system_tickets` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `user_id` bigint(20) NOT NULL,
  `date` datetime NOT NULL,
  `status` varchar(100) NOT NULL DEFAULT 'Aberto',
  `rating` varchar(100) DEFAULT NULL,
  `title` varchar(100) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4;

-- Tabela de mensagens dos tickets
CREATE TABLE IF NOT EXISTS `admin_system_ticket_messages` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `user_id` bigint(20) NOT NULL,
  `name` varchar(100) NOT NULL,
  `message` varchar(500) NOT NULL,
  `date` datetime NOT NULL,
  `idTicket` bigint(20) NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4;

-- Tabela de anúncios
CREATE TABLE IF NOT EXISTS `admin_system_adv` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `user_id` bigint(20) NOT NULL,
  `description` varchar(500) NOT NULL,
  `validity` int(11) NOT NULL,
  `date` datetime NOT NULL,
  `photo` varchar(255) DEFAULT NULL,
  `deleted` tinyint(1) NOT NULL DEFAULT 0,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4;

-- Tabela de organizações
CREATE TABLE IF NOT EXISTS `admin_system_organization` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `blocked` bit(1) DEFAULT b'0',
  `dateCreation` datetime DEFAULT NULL,
  `idOwner` bigint(20) DEFAULT NULL,
  `maxMembers` int(11) DEFAULT NULL,
  `name` varchar(255) DEFAULT NULL,
  `onlineMembers` int(11) DEFAULT 0,
  `urlPhoto` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4;

-- Tabela de blacklist de organizações
CREATE TABLE IF NOT EXISTS `admin_system_organization_blacklist` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` bigint(20) NOT NULL,
  `date` date NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4;

-- Tabela de salários
CREATE TABLE IF NOT EXISTS `admin_system_salary` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `permission` varchar(255) DEFAULT NULL,
  `salary` int(11) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4;

-- Tabela de permissões de organizações
CREATE TABLE IF NOT EXISTS `admin_system_org_permissions` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `idOrganization` bigint(20) DEFAULT NULL,
  `limit` int(11) DEFAULT NULL,
  `name` varchar(255) DEFAULT NULL,
  `permission` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4;

-- Tabela de histórico de compras
CREATE TABLE IF NOT EXISTS `admin_system_purchases_history` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `user_id` bigint(20) NOT NULL,
  `item` varchar(100) NOT NULL,
  `category` varchar(100) NOT NULL,
  `date` datetime NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4;

-- ========================================
-- DADOS INICIAIS
-- ========================================

-- Inserir salários padrão
INSERT IGNORE INTO `admin_system_salary` (`permission`, `salary`) VALUES
('Admin', 5000),
('Police', 3000),
('Paramedic', 2500),
('Premium', 2000);

-- ========================================
-- QUERIES PARA O VRP
-- ========================================

-- Queries para veículos (caso não existam)
CREATE TABLE IF NOT EXISTS `vehicles` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `Passport` int(11) NOT NULL,
  `vehicle` varchar(100) NOT NULL,
  `plate` varchar(20) NOT NULL,
  `work` tinyint(1) NOT NULL DEFAULT 0,
  `engine` int(11) NOT NULL DEFAULT 1000,
  `body` int(11) NOT NULL DEFAULT 1000,
  `fuel` int(11) NOT NULL DEFAULT 100,
  PRIMARY KEY (`id`),
  UNIQUE KEY `plate` (`plate`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Queries para banidos (caso não existam)
CREATE TABLE IF NOT EXISTS `banneds` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `license` varchar(100) NOT NULL,
  `time` int(11) NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `license` (`license`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
