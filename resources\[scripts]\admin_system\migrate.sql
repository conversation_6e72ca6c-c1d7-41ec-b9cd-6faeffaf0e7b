-- ========================================
-- MIGRAÇÃO DE DADOS DO MONKEY_SYSTEM_V2
-- ========================================

-- Execute este script APENAS se você já tem dados no monkey_system_v2
-- e quer migrar para o novo admin_system

-- ========================================
-- MIGRAÇÃO DE TICKETS
-- ========================================

INSERT INTO admin_system_tickets (user_id, date, status, rating, title)
SELECT user_id, date, status, rating, title
FROM monkey_system_tickets
WHERE NOT EXISTS (
    SELECT 1 FROM admin_system_tickets 
    WHERE admin_system_tickets.user_id = monkey_system_tickets.user_id 
    AND admin_system_tickets.date = monkey_system_tickets.date
);

-- ========================================
-- MIGRAÇÃO DE MENSAGENS DE TICKETS
-- ========================================

INSERT INTO admin_system_ticket_messages (user_id, name, message, date, idTicket)
SELECT 
    m.user_id, 
    m.name, 
    m.message, 
    m.date,
    (SELECT a.id FROM admin_system_tickets a 
     JOIN monkey_system_tickets o ON o.user_id = a.user_id AND o.date = a.date 
     WHERE o.id = m.idTicket LIMIT 1) as idTicket
FROM monkey_system_ticket_messages m
WHERE EXISTS (
    SELECT 1 FROM admin_system_tickets a 
    JOIN monkey_system_tickets o ON o.user_id = a.user_id AND o.date = a.date 
    WHERE o.id = m.idTicket
);

-- ========================================
-- MIGRAÇÃO DE ANÚNCIOS
-- ========================================

INSERT INTO admin_system_adv (user_id, description, validity, date, photo, deleted)
SELECT user_id, description, validity, date, photo, deleted
FROM monkey_system_adv
WHERE NOT EXISTS (
    SELECT 1 FROM admin_system_adv 
    WHERE admin_system_adv.user_id = monkey_system_adv.user_id 
    AND admin_system_adv.date = monkey_system_adv.date
);

-- ========================================
-- MIGRAÇÃO DE ORGANIZAÇÕES
-- ========================================

INSERT INTO admin_system_organization (blocked, dateCreation, idOwner, maxMembers, name, onlineMembers, urlPhoto)
SELECT blocked, dateCreation, idOwner, maxMembers, name, onlineMembers, urlPhoto
FROM monkey_system_organization
WHERE NOT EXISTS (
    SELECT 1 FROM admin_system_organization 
    WHERE admin_system_organization.name = monkey_system_organization.name
);

-- ========================================
-- MIGRAÇÃO DE BLACKLIST DE ORGANIZAÇÕES
-- ========================================

INSERT INTO admin_system_organization_blacklist (user_id, date)
SELECT user_id, date
FROM monkey_system_organization_blacklist
WHERE NOT EXISTS (
    SELECT 1 FROM admin_system_organization_blacklist 
    WHERE admin_system_organization_blacklist.user_id = monkey_system_organization_blacklist.user_id
);

-- ========================================
-- MIGRAÇÃO DE SALÁRIOS
-- ========================================

INSERT INTO admin_system_salary (permission, salary)
SELECT permission, salary
FROM monkey_system_salary
WHERE NOT EXISTS (
    SELECT 1 FROM admin_system_salary 
    WHERE admin_system_salary.permission = monkey_system_salary.permission
);

-- ========================================
-- MIGRAÇÃO DE HISTÓRICO DE COMPRAS
-- ========================================

INSERT INTO admin_system_purchases_history (user_id, item, category, date)
SELECT user_id, item, category, date
FROM monkey_system_purchases_history
WHERE NOT EXISTS (
    SELECT 1 FROM admin_system_purchases_history 
    WHERE admin_system_purchases_history.user_id = monkey_system_purchases_history.user_id 
    AND admin_system_purchases_history.date = monkey_system_purchases_history.date
);

-- ========================================
-- VERIFICAÇÃO DA MIGRAÇÃO
-- ========================================

SELECT 
    'Tickets migrados' as tipo,
    COUNT(*) as quantidade
FROM admin_system_tickets
UNION ALL
SELECT 
    'Anúncios migrados' as tipo,
    COUNT(*) as quantidade
FROM admin_system_adv
UNION ALL
SELECT 
    'Organizações migradas' as tipo,
    COUNT(*) as quantidade
FROM admin_system_organization
UNION ALL
SELECT 
    'Salários migrados' as tipo,
    COUNT(*) as quantidade
FROM admin_system_salary;

-- ========================================
-- MENSAGEM DE SUCESSO
-- ========================================

SELECT 'MIGRAÇÃO CONCLUÍDA COM SUCESSO!' as status;
