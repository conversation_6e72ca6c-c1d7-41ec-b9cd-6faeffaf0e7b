﻿<!doctype html>
<html class="no-js" lang="en">
<head>
   <meta charset="utf-8">
   <meta http-equiv="X-UA-Compatible" content="IE=Edge">
   <meta content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no" name="viewport">
   <meta name="viewport" content="width=device-width, initial-scale=1.0">
   <title>Monkey System</title>
   <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.0.2/dist/css/bootstrap.min.css" rel="stylesheet">
   <link href="https://code.jquery.com/ui/1.13.1/themes/smoothness/jquery-ui.css" rel="stylesheet">
   <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/icons_ms728/icons.css">
   <link rel="stylesheet" href="css/main.css">
   <link rel="stylesheet" href="css/color_skins.css">
   <link rel="stylesheet" href="css/style.css">
   <link rel="stylesheet" href="css/bootstrap-select.css">
</head>
<body class="theme-black" style="background-color: transparent !important;">
   <div id="app" style="display: none;">
      <div class="corpo">
         <!-- Page Loader -->
         <div class="page-loader-wrapper">
            <div class="loader">
               <img :src="urlLogo" width="150" alt="Monkey">
               <p class="mt-5">Carregando...</p>
            </div>
         </div>

         <div class="overlay"></div><!-- Overlay For Sidebars -->

         <!-- Left Sidebar -->
         <aside id="minileftbar" class="minileftbar">
            <ul class="menu_list">
               <li>
                  <a href="javascript:void(0);" class="bars"></a>
               </li>
               <li>
                  <a class="navbar-brand" href="javascript:;"><img src="images/logo-icon.png" alt="Monkey"></a>
               </li>
               <li>
                  <a href="javascript:void(0);" class="menu-sm"><i class="fas fa-exchange normal"></i></a>
               </li>        
               <li class="menuapp-btn">
                  <a href="javascript:void(0);" v-if="permissao.NV1"><i class="far fa-wifi normal"></i></a>
               </li>
               <li class="notifications">
                  <a href="javascript:void(0);"><i class="fal fa-info-circle normal font-16"></i></a>
               </li>
               <!-- <li class="task">
                  <a href="javascript:void(0);"><i class="far fa-flag"></i></a>
               </li> -->
               <li class="power">
                  <a href="javascript:void(0);" class="js-right-sidebar"><i class="fas fa-cog zmdi-hc-spin"></i></a>            
                  <a href="javascript:;" @click="close" class="mega-menu"><i class="fas fa-power-off"></i></a>
               </li>
            </ul>    
         </aside>

         <aside class="right_menu">
            <div class="menu-app overflow-auto">
               <div class="card" v-if="permissao.NV1">
                  <div class="row">
                     <div class="col-12 mb-1">
                        <div class="header pb-2">
                           <h2><strong>Jogadores Online</strong> ({{ usersOnline.jogador != null ? usersOnline.jogador.length : 0 }})</h2>
                        </div>
                        <div class="body float-start px-3 py-2 me-1 mb-1" v-for="(idUser, index) in usersOnline.jogador">{{ idUser }}</div>
                     </div>
               
                     <div class="col-12 mb-1">
                        <div class="header pb-2">
                           <h2><strong>Staff Online</strong> ({{ usersOnline.staff != null ? usersOnline.staff.length : 0 }})</h2>
                        </div>
                        <div class="body float-start px-3 py-2 me-1 mb-1" v-for="(idUser, index) in usersOnline.staff">{{ idUser }}</div>
                     </div>
               
                     <div class="col-12 mb-1">
                        <div class="header pb-2">
                           <h2><strong>Polícia Online</strong> ({{ usersOnline.policia != null ? usersOnline.policia.length : 0 }})</h2>
                        </div>
                        <div class="body float-start px-3 py-2 me-1 mb-1" v-for="(idUser, index) in usersOnline.policia">{{ idUser }}</div>
                     </div>
            
                     <div class="col-12 mb-1">
                        <div class="header pb-2">
                           <h2><strong>Hospital Online</strong> ({{ usersOnline.hospital != null ? usersOnline.hospital.length : 0 }})</h2>
                        </div>
                        <div class="body float-start px-3 py-2 me-1 mb-1" v-for="(idUser, index) in usersOnline.hospital">{{ idUser }}</div>
                     </div>
                  </div>
               </div>
            </div>

            <div class="notif-menu overflow-auto">
               <div class="card">
                  <div class="header">
                     <h2><strong>Sobre os desenvolvedores</strong></h2>
                  </div>
                  <div class="body">
                     <img src="https://cdn.discordapp.com/attachments/928755706235064350/935182233780768838/perfil.png" class="w-100 mb-2">
                     <div class="font-13 text-center text-secondary">
                        <strong class="font-18">Monkey System</strong><br>
                        <small class="mt-1">by Creative Monkey</small><br>
                        <br>
                        <i class="fab fa-discord normal font-18"></i><br>
                        <small>Nosso discord</small><br>
                        <strong>discord.gg/5NR4k3WdnV</strong>
                     </div>
                  </div>
               </div>
            </div>

            <div class="task-menu overflow-auto">
               <div class="card tasks">
                  <div class="header">
                     <h2><strong>Project</strong> Status</h2>
                  </div>
                  <div class="body">
                     
                  </div>
               </div>
            </div>

            <div id="rightsidebar" class="right-sidebar">
               <div class="p-4">
                  <div class="card">
                     <div class="header pt-1">
                        <h2><strong>Cor da</strong> Skin</h2>
                     </div>
                     <div class="body">
                        <ul class="choose-skin list-unstyled m-b-0">
                           <li data-theme="black" class="active">
                              <div class="black"></div>
                           </li>
                           <li data-theme="purple">
                              <div class="purple"></div>
                           </li>                   
                           <li data-theme="blue">
                              <div class="blue"></div>
                           </li>
                           <li data-theme="cyan">
                              <div class="cyan"></div>                    
                           </li>
                           <li data-theme="green">
                              <div class="green"></div>
                           </li>
                           <li data-theme="orange">
                              <div class="orange"></div>
                           </li>
                           <li data-theme="blush">
                              <div class="blush"></div>                    
                           </li>
                        </ul>
                     </div>
                  </div>                
                  <div class="card">
                     <div class="header">
                        <h2><strong>Tema</strong> Lateral</h2>
                     </div>
                     <div class="body theme-light-dark">
                        <button class="t-dark btn btn-primary btn-round btn-block">Dark</button>
                     </div>
                  </div>
               </div>               
            </div>

            <div id="leftsidebar" class="sidebar">
               <div class="menu">
                  <ul class="list">
                     <li>
                        <div class="user-info m-b-20">
                           <div class="image mb-3">
                              <a href="javascript:;"><img :src="urlLogo" alt="Monkey"></a>
                           </div>
                           <div class="detail mb-1">
                              <h6>Painel de Controle</h6>
                           </div>
                        </div>
                     </li>
                     <li class="header">Principal</li>
                     <li><a href="javascript:;" @click="changePage('Dashboard', 'Principal', 'pages/dashboard.html')">
                        <i class="far fa-home"></i><span>Dashboard</span></a>
                     </li>
                     <li><a href="javascript:void(0);" class="menu-toggle"><i class="far fa-user"></i><span>Meu Painel</span></a>
                        <ul class="ml-menu">
                           <li><a href="javascript:;" @click="changePage('Sobre', 'Meu Painel', 'pages/meuPainel_Sobre.html')">Sobre</a></li>
                           <li><a href="javascript:;" @click="changePage('Advertências', 'Meu Painel', 'pages/meuPainel_Advs.html')">Advertências</a></li>
                           <li><a href="javascript:;" @click="changePage('Tickets', 'Meu Painel', 'pages/meuPainel_Tickets.html')">Tickets</a></li>
                        </ul>
                     </li>

                     <li class="header" v-if="permissao.NV1 || permissao.NV2 || permissao.NV3 || permissao.NV4">Gerenciamento</li>
                     <li>
                        <a href="javascript:void(0);" class="menu-toggle" :class="permissao.NV1 || permissao.NV2 || permissao.NV3 || permissao.NV4 ? '' : 'd-none'">
                           <i class="far fa-users"></i><span>Jogadores</span>
                        </a>
                        <ul class="ml-menu">
                           <li v-if="permissao.NV1 || permissao.NV2 || permissao.NV3 || permissao.NV4">
                              <a href="javascript:;" @click="changePage('Pesquisa', 'Jogadores', 'pages/jogadores_Pesquisa.html')">Pesquisa</a>
                           </li>
                           <li v-if="permissao.NV1 || permissao.NV2">
                              <a href="javascript:;" @click="changePage('Itens', 'Jogadores', 'pages/jogadores_Itens.html')">Itens</a>
                           </li>
                           <li v-if="permissao.NV1 || permissao.NV2">
                              <a href="javascript:;" @click="changePage('Veículos', 'Jogadores', 'pages/jogadores_Veiculos.html')">Veículos</a>
                           </li>
                           <li v-if="permissao.NV1 || permissao.NV2">
                              <a href="javascript:;" @click="changePage('Permissões', 'Jogadores', 'pages/jogadores_Permissoes.html')">Permissões</a>
                           </li>
                        </ul>
                     </li>
                     <li v-if="permissao.NV1">
                        <a href="javascript:;" @click="changePage('Vips', 'Gerenciamento', 'pages/gerenciamento_Vips.html')"><i class="far fa-star"></i><span>Vips</span></a>
                     </li>            
                     <li v-if="permissao.NV1">
                        <a href="javascript:;" @click="changePage('Salários', 'Gerenciamento', 'pages/gerenciamento_Salarios.html')"><i class="far fa-dollar-sign"></i><span>Salários</span></a>
                     </li>
                     <li v-if="permissao.NV1 || permissao.NV2 || permissao.NV3">
                        <a href="javascript:;" @click="changePage('Anúncios', 'Gerenciamento', 'pages/gerenciamento_Anuncios.html')"><i class="far fa-bullhorn"></i><span>Anúncios</span></a>
                     </li>
                     <li v-if="permissao.NV1 || permissao.NV2">
                        <a href="javascript:;" @click="changePage('Tickets', 'Gerenciamento', 'pages/gerenciamento_Tickets.html')"><i class="far fa-headset"></i><span>Tickets</span></a>
                     </li>
                     <li v-if="permissao.NV1 || permissao.NV2 || permissao.NV3">
                        <a href="javascript:;" @click="changePage('Advertências', 'Gerenciamento', 'pages/gerenciamento_Advs.html')"><i class="far fa-exclamation-triangle"></i><span>Advertências</span></a>
                     </li>

                     <!-- <li class="header">Loja</li>
                     <li v-for="(c, index) in ecommerce.categorias" :key="index">
                        <a href="javascript:;" @click="changePage(c.categoria, 'Loja', 'pages/loja.html')">
                           <i class="fal fa-long-arrow-right"></i><span>{{ c.categoria }}</span>
                        </a>
                     </li>
                     <li v-if="ecommerce.categorias.length > 0">
                        <a href="javascript:;" @click="changePage('Hístórico', 'Loja', 'pages/loja_Historico.html')"><i class="far fa-history"></i><span>Hístórico</span></a>
                     </li> -->
                     <!-- <li>
                        <a href="javascript:;" @click="changePage('Imobiliária', 'Loja', 'pages/loja_Imobiliaria.html')">
                           <i class="far fa-clinic-medical"></i><span>Imobiliária</span>
                        </a>
                     </li> -->
                  </ul>
               </div>
            </div>
         </aside>

         <!-- Carregando -->
         <div class="loadingContent">
            <div class="loading">
               <p>Carregando</p><span></span>
            </div>
         </div>

         <!-- Main Content -->
         <section class="content p-0 overflow-hidden">
            <div class="block-header mb-0 mt-2 mx-1 px-4 py-3">
               <h2>{{ page.titulo }}</h2>
               <ul class="breadcrumb padding-0">
                  <li class="breadcrumb-item"><a href="javascript:;"><i class="far fa-home normal"></i></a></li>
                  <li class="breadcrumb-item"><a href="javascript:void(0);">{{ page.menu }}</a></li>
                  <li class="breadcrumb-item active">{{ page.titulo }}</li>
               </ul>
            </div>

            <div id="content-body"></div>
         </section>
      </div>
   </div>

   <script src="script/libscripts.bundle.js"></script>
   <script src="script/vendorscripts.bundle.js"></script>
   <script src="script/mainscripts.bundle.js"></script>
   <script src="script/bootstrap-select.js"></script>
   <script src="https://cdn.jsdelivr.net/npm/lazyload@2.0.0-rc.2/lazyload.js"></script>
   <script src="https://cdn.jsdelivr.net/npm/vue@2"></script>
   <script src="script/functions.js"></script>
   <script src="https://code.jquery.com/ui/1.13.1/jquery-ui.min.js"></script>
</body>
</html>